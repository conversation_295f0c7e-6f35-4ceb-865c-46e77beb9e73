<!DOCTYPE html>
<html lang="en">
   <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Invoice</title>
      <style>
         body {
         font-size: 14px;
         margin: 0;
         padding: 0;
         }
         table {
         width: 100%;
         border-collapse: collapse;
         }
         table, th, td {
         border: 1px solid #646464; /* Apply borders to table, th, and td */
         }
         th, td {
         padding: 8px; /* Add padding for readability */
         }
         th {
         background-color: #f4f4f4; /* Light background for headers */
         }
      </style>
   </head>
   <body>
      <div style="width:100%;margin:0;padding:0;font-size:14px;">
         <div style="margin:0;padding:0;">
            <div style="width:100%;text-align:center;margin-top:2px;">
               <h2 style="margin-bottom: 0;margin-top: 0px;">{{$data['arrSocietyDetail']['soc_name']}}</h2>
               <div style="width:100%;text-align:center;line-height:1.5;color:#000000;margin-top:5px;margin-bottom:15px;margin-right:0px;margin-left:0px;font-size: 11px;">
                  Reg No: {{$data['arrSocietyDetail']['soc_reg_num']}}
                  @isset($data['arrSocietyDetail']['soc_gst_number'])
                  | GSTIN/UIN: {{$data['arrSocietyDetail']['soc_gst_number']}}
                  @endisset
                  <br/>
                  {{$data['arrSocietyDetail']['soc_address_1']}}, {{$data['arrSocietyDetail']['soc_address_2']}}, {{$data['arrSocietyDetail']['soc_landmark']}}, {{$data['arrSocietyDetail']['soc_city_or_town']}}, {{$data['arrSocietyDetail']['soc_state']}}, {{$data['arrSocietyDetail']['soc_pincode']}}.<br>
                  @isset($data['arrSocietyDetail']['soc_office_email'])
                  Email: {{$data['arrSocietyDetail']['soc_office_email']}}
                  @endisset
                  @isset($data['arrSocietyDetail']['soc_office_mobile'])
                  | Phone: {{$data['arrSocietyDetail']['soc_office_mobile']}}
                  @endisset
               </div>
               @isset($data['arrSocietyDetail']['year'])
               {{$data['arrSocietyDetail']['year']}}
               @endisset
            </div>
            <div style="clear:both;"></div>
            <div style="width:50%;float:left;margin-top:0;">
               <table style="border: none; border-collapse: collapse; width: 100%;">
                  <tbody>
                     <tr>
                        <td style="border: none;"><strong>To,</strong></td>
                     </tr>
                     <tr>
                        <td style="border: none;"><strong>Name:</strong></td>
                        <td style="border: none;"><strong>{{ $data['member_first_name'] ?? '' }} {{ $data['member_last_name'] ?? '' }}</strong></td>
                     </tr>
                     <tr>
                        <td style="border: none;"><strong>Unit Number:</strong></td>
                        <td style="border: none;">{{ $data['soc_building_name'] ?? '' }} / {{ $data['unit_flat_number'] ?? '' }}</td>
                     </tr>
                     <tr>
                        <td style="border: none;"><strong>GSTIN/UIN:</strong></td>
                        <td style="border: none;">{{ $data['gstin'] ?? '' }}</td>
                     </tr>
                  </tbody>
               </table>
            </div>
            <div style="width:50%;float:right;">
               <table style="border: none; border-collapse: collapse; width: 100%;">
                  <tbody>
                     <tr>
                        <td style="border: none;"><strong>Invoice No:</strong></td>
                        <td style="border: none;"># {{ $data['invoice_number'] ?? '' }}</td>
                     </tr>
                     <tr>
                        <td style="border: none;"><strong>Invoice Date:</strong></td>
                        <td style="border: none;">{{ $data['bill_date'] ?? '' }}</td>
                     </tr>
                     <tr>
                        <td style="border: none;"><strong>Due Date:</strong></td>
                        <td style="border: none;">{{ $data['due_date'] ?? '' }}</td>
                     </tr>
                     <tr>
                        <td style="border: none;"><strong>Invoicing Period:</strong></td>
                        <td style="border: none;">{{ $data['from_date'] ?? '' }} to {{ $data['to_date'] ?? '' }}</td>
                     </tr>
                  </tbody>
               </table>
            </div>
            <div style="clear:both;"></div>
            <div style="width:100%;">
               <table>
                  <thead>
                     <tr>
                        <th colspan="6">Particulars</th>
                        <th>Amount (RS.)</th>
                     </tr>
                  </thead>
                  <tbody>
                     <tr>
                        <td colspan="6">{{$data['particular']}}</td>
                        <td style="text-align:right;">{{$data['amount']}}</td>
                     </tr>
                     <tr>
                        <td colspan="6">Delayed Payment Charges ( on amount {{$data['principal_amount']}} )	</td>
                        <td style="text-align:right;">{{$data['interest_amount']}}</td>
                     </tr>
                     <tr>
                        <td rowspan="7" colspan="5">
                           <strong>CHEQUE / NEFT / IMPS Payment Instruction:</strong><br>
                           <p>Account Number: {{ $data['arrAccountDetail']['account_number'] ?? '' }}</p>
                           <p>Bank Name: {{ $data['arrAccountDetail']['bank_name'] ?? '' }}</p>
                           <p>Branch: {{ $data['arrAccountDetail']['branch'] ?? '' }}</p>
                           <p>IFSC: {{ $data['arrAccountDetail']['bank_ifsc'] ?? '' }}</p>
                           <div style="border: 1px dashed #4c5154; background: #dde0e2; display: inline-block; padding-top: 5px; padding-bottom: 5px; padding-left: 10px; padding-right: 10px; margin-top:5px; margin-bottom: 5px; font-size: 11px; border-top-right-radius: 14px; border-bottom-left-radius: 14px; height: 25px;vertical-align: middle;">
                              <p style="">   
                                 <span>Please Pay </span>
                                 <span style="font-weight: bold;">Rs. {{$data['balance_due']}}/-</span>
                              </p>
                              <p style="line-height:1; margin-top:0px; margin-bottom: 0px; padding-bottom:2px; padding-top: 2px; float: left; margin-left: 10px;color:black;">
                                 <span>Before</span>
                                 <span style="font-weight: bold;">
                                 {{$data['due_date']}}                                                                                                          </span>
                              </p>
                           </div>
                        </td>
                        <td><strong>Invoice Amount:</strong></td>
                        <td style="text-align:right;">{{$data['invoice_amount']}}</td>
                     </tr>
                     <tr>
                        <td>Advance Amount:</td>
                        <td style="text-align:right;">(-) {{$data['advance_amount']}}</td>
                     </tr>
                     <tr>
                        <td><strong>Grand Total</strong></td>
                        <td style="text-align:right;"><strong>{{$data['grand_total']}}</strong></td>
                     </tr>
                     <tr>
                        <td>Principal Arrears::</td>
                        <td style="text-align:right;">(+) {{$data['outstanding_principal']}}</td>
                     </tr>
                     <tr>
                        <td>Intrest Arrears:</td>
                        <td style="text-align:right;">(+) {{$data['outstanding_interest']}}</td>
                     </tr>
                     <tr>
                        <td><strong>Balance Due:</strong></td>
                        <td style="text-align:right;"><strong>{{$data['balance_due']}}</strong></td>
                     </tr>
                     <tr>
                        <td colspan="2"><strong>Amount In Words:</strong> {{$data['total_due_in_words']}}</td>
                     </tr>
                  </tbody>
               </table>
            </div>
            <div style="width:100%;padding-top:5px;font-size:13px;">
               <p style="line-height:12px;margin-top:0;margin-bottom:0;">
                  <strong>Note:</strong><br>
                  @foreach ($data['arrInvoiceGeneralNote'] ?? [] as $item)
                  @if (!empty($item)) <!-- Skip empty lines -->
                  {!! nl2br(e($item)) !!}<br>
                  @endif
                  @endforeach
               </p>
            </div>
            <div style="clear:both;"></div>
            <div style="clear:both;" ></div>
            <div style="width: 100%;">
               <div style="line-height:21px; margin-top:0px; margin-bottom: 0px; text-align: right;">
                  For Futurescape Technologies Pvt Ltd<br><br>
                  <span style="margin: 0 15px;">Cubeoneapp</span>
               </div>
            </div>
         </div>
      </div>
   </body>
</html>