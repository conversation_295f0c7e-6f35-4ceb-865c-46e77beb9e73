<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profit And Loss</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h2, .header p, .header h3 {
            margin: 5px 0;
        }
        .table-container {
            width: 100%;
            border-collapse: collapse;
            margin: 0 auto;
        }
        .table-container th, .table-container td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
            font-size: 14px;
        }
        .table-container th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .table-container tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .report-footer {
            margin-top: 20px;
            text-align: right;
            font-size: 14px;
        }
        .report-footer p {
            margin: 0;
        }
    </style>
</head>
<body>
    <!-- <div class="header">
        <h2>NATRAJ CO-OPERATIVE HOUSING SOCIETY LTD.</h2>
        <p>[REGN.NO:NBOM/CIDCO/HSG(OH)/588/JTR/1998 DATED 07/01/1998]</p>
        <p>Plot No-35, Sector-11, Kharghar, Navi Mumbai, Maharashtra-410210</p>
        <h3>Trial Balance for {{ $reportYear ?? '2024-2025' }}</h3>
    </div> -->
    <div style="width:100%;text-align:center;margin-top:2px;">
                <h2 style="margin-bottom: 0;margin-top: 0px;">{{$company_details['soc_name']}}</h2>
                <div style="width:100%;text-align:center;line-height:1.5;color:#000000;margin-top:5px;margin-bottom:15px;margin-right:0px;margin-left:0px;font-size: 11px;">
                    Reg No: {{$company_details['soc_reg_num']}}
                    @isset($company_details['soc_gst_number'])
                     | GSTIN/UIN: {{$company_details['soc_gst_number']}}
                    @endisset
                     <br/>
                    {{$company_details['soc_address_1']}}, {{$company_details['soc_address_2']}}, {{$company_details['soc_landmark']}}, {{$company_details['soc_city_or_town']}}, {{$company_details['soc_state']}}, {{$company_details['soc_pincode']}}.<br>
                    @isset($company_details['soc_office_email'])
                    Email: {{$company_details['soc_office_email']}}
                    @endisset
                    @isset($company_details['soc_office_mobile'])
                    | Phone: {{$company_details['soc_office_mobile']}}
                    @endisset
                </div>
                @isset($company_details['year'])
                     {{$company_details['year']}}
                    @endisset

            </div>

    <table class="table-container">
        <thead>
            <tr>
                <th>{{$headings[0]}}</th>
                <th>{{$headings[1]}}</th>
                <th>{{$headings[2]}}</th>
                <th>{{$headings[4]}}</th>
                <th>{{$headings[5]}}</th>
                <th>{{$headings[6]}}</th>
            </tr>
        </thead>
        <tbody>
            @forelse ($data as $row)
                <tr>
                    <td>{{ $row['ledger_account_name'] ?? '-' }}</td>
                    <td>{{ $row['start_year_1'] ?? '-' }}</td>
                    <td>{{ $row['end_year_1'] ?? '-' }}</td>
                    <td>{{ $row['ledger_account_name1'] ?? '-' }}</td>
                    <td>{{ $row['start_year_2'] ?? '-' }}</td>
                    <td>{{ $row['end_year_2'] ?? '-' }}</td>
                </tr>
            @empty
                <tr>
                    <td colspan="7">No data available</td>
                </tr>
            @endforelse
        </tbody>
    </table>

    <div class="report-footer">
        <p>Report Date: {{ $reportDate ?? now()->format('d-m-Y') }}</p>
    </div>
</body>
</html>
