<?php

/* 
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
?>
<style>
  /* Ensure the table header repeats on every printed page */
  table thead { 
    display: table-header-group; 
  }

  /* Prevent a page break between your report title/logo and the table */
  .report-header {
    page-break-after: avoid;
  }
</style>
<?php
$i=1;
     if(isset($data) && !empty($data)) { ?>
<div style="margin-top:0px;margin-bottom:0px;margin-right:0px;margin-left:0px;padding-top:0px;padding-bottom:0px;padding-right:0px;padding-left:0px;font-size:14px;" >
			<div style="width:100%;" >
                            <div class="report-header" style="width:100%;text-align:center;margin-top:2px;">  
                                <?php if(!empty($imageLogoUrl)) { ?>
                                    <img src="<?php echo $imageLogoUrl; ?>" alt="complex logo" style="max-height: 60px;" /> <br>
                                <?php } ?>
                                <h2 style="margin-bottom: 0;margin-top: 0px;">
                        <?php echo (isset($company_details['society_name']) && !empty(trim($company_details['society_name'])))? ucwords($company_details['society_name']):'';?></h2>
				<div style="width:100%;text-align:center;line-height:1.5;color:#000000;margin-top:5px;margin-bottom:15px;margin-right:0px;margin-left:0px;">
                                    <strong>Maintenance Due Report : <?php echo date('Y-m-d');?></strong><br/>
                                </div> 
                            </div>
			<div style="min-height:950px !important;max-width:1170px;margin-top:0;margin-bottom:0;margin-right:auto;margin-left:auto;padding-left:30px;padding-right:30px;" >
				<div style="width:100%; margin-bottom: 20px;" >
					<table style="border-collapse:collapse;border-spacing:0;width:100%;" >
						<thead>
							<tr>
								<th style="border-width:1px;border-style:solid;border-color:#DDDDDD;border-bottom-width:2px;border-bottom-style:solid;border-bottom-color:#dddddd;line-height:1.42857;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:4px;vertical-align:top;background: #f4f4f4;text-align: center;">Sr. No.</th>
								<th style="border-width:1px;border-style:solid;border-color:#DDDDDD;border-bottom-width:2px;border-bottom-style:solid;border-bottom-color:#dddddd;line-height:1.42857;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:4px;vertical-align:top;background: #f4f4f4;text-align: center;">Building / Unit</th>
<!--								<th style="border-width:1px;border-style:solid;border-color:#DDDDDD;border-bottom-width:2px;border-bottom-style:solid;border-bottom-color:#dddddd;line-height:1.42857;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:4px;vertical-align:top;background: #f4f4f4;text-align: center;">Flat</th>-->
                                                                <th style="border-width:1px;border-style:solid;border-color:#DDDDDD;border-bottom-width:2px;border-bottom-style:solid;border-bottom-color:#dddddd;line-height:1.42857;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:4px;vertical-align:top;background: #f4f4f4;text-align: center;">Member</th>
								<th style="border-width:1px;border-style:solid;border-color:#DDDDDD;border-bottom-width:2px;border-bottom-style:solid;border-bottom-color:#dddddd;line-height:1.42857;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:4px;vertical-align:top;background: #f4f4f4;text-align: center;">Total Due (RS.)</th>
                                                                <th style="border-width:1px;border-style:solid;border-color:#DDDDDD;border-bottom-width:2px;border-bottom-style:solid;border-bottom-color:#dddddd;line-height:1.42857;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:4px;vertical-align:top;background: #f4f4f4;text-align: center;">Advance (RS.)</th>
							</tr>
						</thead>
						<tbody>
                                                    <?php foreach( $data as $eachMemberIncomeDetail) {?>
							<tr>
								<td style="border-width:1px;border-style:solid;border-color:#DDDDDD;line-height:1.42857;padding-top:2px;padding-bottom:2px;padding-right:2px;padding-left:2px;vertical-align:top;text-align: center;" ><?php echo $i;?></td>
								<td style="border-width:1px;border-style:solid;border-color:#DDDDDD;line-height:1.42857;padding-top:2px;padding-bottom:2px;padding-right:2px;padding-left:2px;vertical-align:top;" ><?php echo ucwords($eachMemberIncomeDetail['unit_name']); ?></td>
<!--								<td style="border-width:1px;border-style:solid;border-color:#DDDDDD;line-height:1.42857;padding-top:2px;padding-bottom:2px;padding-right:2px;padding-left:2px;vertical-align:top;text-align: right;" ><?php echo ucwords($eachMemberIncomeDetail['unit_name']); ?></td>-->
								<td style="border-width:1px;border-style:solid;border-color:#DDDDDD;line-height:1.42857;padding-top:2px;padding-bottom:2px;padding-right:2px;padding-left:2px;vertical-align:top;" ><?php echo ucwords($eachMemberIncomeDetail['member_name']); ?></td>
								<td style="border-width:1px;border-style:solid;border-color:#DDDDDD;line-height:1.42857;padding-top:2px;padding-bottom:2px;padding-right:2px;padding-left:2px;vertical-align:top;text-align: right;" ><?php echo $eachMemberIncomeDetail['due_amount']; ?></td>
                                                                <td style="border-width:1px;border-style:solid;border-color:#DDDDDD;line-height:1.42857;padding-top:2px;padding-bottom:2px;padding-right:2px;padding-left:2px;vertical-align:top;text-align: right;" ><?php echo $eachMemberIncomeDetail['advances']; ?></td>
							</tr>
                                                    <?php $i++; } ?>
						</tbody>
					</table>
				</div>
			</div>

		</div>
	</div>
     <?php } 
     else
    {
        echo 'No record found.';
    }?>
