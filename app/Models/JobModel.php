<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class JobModel extends Model
{
    use HasFactory;

    protected $table = 'jobs';

    protected $fillable = [
        'task_id',
        'user_id',
        'email_or_phone',
        'type',
        'subject',
        'content',
        'url',
        'cc_users',
        'bcc_users',
        'status',
        'is_failed',
        'failed_reason',
        'created_at',
        'updated_at',
    ];

    public function task()
    {
        return $this->belongsTo(TaskModel::class, 'task_id', 'id');
    }
}
