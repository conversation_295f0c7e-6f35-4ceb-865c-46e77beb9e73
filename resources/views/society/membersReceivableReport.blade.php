<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $society_name ?? 'Society Report' }}</title>
    <link rel="shortcut icon" href="https://stgsociety.chsone.in/images/favicon.png" />
    <style>
        html { font-size: 62.5%; margin: 0; }
        body { font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; font-size: 12px; margin: 0; color: #333; background: #fff; }
        table { background: white; border: solid 1px #ddd; width: 100%; margin: 20px 0; border-collapse: collapse; }
        table th, table td { padding: 8px; border: 1px solid #ddd; text-align: left; }
        table thead { background-color: #f0f0f0; }
        .printOption { background: #001941; height: 40px; padding: 10px; color: #fff; }
        .footer { background: #001941; padding: 10px; color: #fff; text-align: right; }
        .section-header { font-weight: bold; text-align: left; background: #f9f9f9; font-size: 1.1em; }
        .main-title { text-align: center; font-size: 1.6em; font-weight: bold; margin-top: 20px; }
        .sub-title { text-align: center; font-size: 1.2em; margin-bottom: 20px; }
        .report-info { text-align: center; margin-bottom: 20px; }
        .report-info div { margin: 5px 0; }
        .summary-section { margin-top: 30px; }
        .summary-title { font-weight: bold; font-size: 1.3em; margin-bottom: 15px; }
        .summary-table { background-color: #f9f9f9; }
        .summary-table th, .summary-table td { background-color: #f9f9f9; font-weight: bold; }
        .total-row { background-color: #e9e9e9; font-weight: bold; }
        .report-date { text-align: left; margin-top: 20px; font-weight: bold; }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
    </style>
</head>
<body>
    <div class="printOption">
        <p>{{ 'Members Receivable' ?? 'Society Report' }}</p>
    </div>
    
    <div class="main-title">{{ $company_details['society_name'] ?? 'SOCIETY NAME' }}</div>
    
    @if(isset($reg_number) || isset($gstin))
    <div class="report-info">
        @if(isset($reg_number))
        <div>Reg No: {{ $reg_number }}</div>
        @endif
        @if(isset($gstin))
        <div>GSTIN/UIN: {{ $gstin }}</div>
        @endif
    </div>
    @endif
    
    @if(isset($report_title))
    <div class="sub-title">{{ $report_title }}</div>
    @endif
    
    @if(isset($date_range))
    <div class="text-center">
        <strong>Date: {{ $date_range }}</strong>
    </div>
    @endif
    
    <div class="tableDiv" style="width: 98%; margin: 0 auto;">
        @if(isset($data) && is_array($data) && count($data) > 0)
            @php
                $reportData = $data[0] ?? [];
                $totalsData = $data[1] ?? [];

                // Get dynamic headers from first row of data
                $headers = [];
                if (!empty($reportData)) {
                    $firstRow = reset($reportData);
                    if (is_array($firstRow)) {
                        $allHeaders = array_keys($firstRow);

                        // Define custom order for columns
                        $orderedHeaders = [
                            'building_unit_name',
                            'member_name',
                            'maintenance_due',
                            'indental_due',
                            'cr_bal',
                            'ledger_bal'
                        ];

                        // Filter and order headers based on what exists in data
                        $headers = [];
                        foreach ($orderedHeaders as $orderedHeader) {
                            if (in_array($orderedHeader, $allHeaders)) {
                                $headers[] = $orderedHeader;
                            }
                        }

                        // Add any remaining headers that weren't in our ordered list
                        foreach ($allHeaders as $header) {
                            if (!in_array($header, $headers) && !in_array($header, ['id', 'soc_building_id', 'unit_id', 'maintainance_due', 'ledger_balance'])) {
                                $headers[] = $header;
                            }
                        }
                    }
                }
            @endphp
            
            @if(!empty($headers) && !empty($reportData))
            <table>
                <thead>
                    <tr>
                        @foreach($headers as $header)
                            @if(!in_array($header, ['id', 'soc_building_id', 'unit_id', 'maintainance_due', 'ledger_balance']))
                                <th>
                                    @switch($header)
                                        @case('building_unit_name')
                                            Building / Unit
                                            @break
                                        @case('member_name')
                                            Primary Member
                                            @break
                                        @case('maintenance_due')
                                            Maintenance Dues
                                            @break
                                        @case('indental_due')
                                            Incidental Dues
                                            @break
                                        @case('cr_bal')
                                            Credit Balance
                                            @break
                                        @case('ledger_bal')
                                            Ledger Balance
                                            @break
                                        @default
                                            {{ ucwords(str_replace('_', ' ', $header)) }}
                                    @endswitch
                                </th>
                            @endif
                        @endforeach
                    </tr>
                </thead>
                <tbody>
                    @foreach($reportData as $row)
                        <tr>
                            @foreach($headers as $header)
                                @if(!in_array($header, ['id', 'soc_building_id', 'unit_id', 'maintainance_due', 'ledger_balance']))
                                    <td class="{{ in_array($header, ['maintenance_due', 'indental_due', 'cr_bal', 'ledger_bal']) ? 'text-right' : '' }}">
                                        @if(in_array($header, ['maintenance_due', 'indental_due', 'cr_bal', 'ledger_bal']) && is_numeric($row[$header]))
                                            {{ number_format($row[$header], 2) }}
                                        @else
                                            {{ $row[$header] ?? '' }}
                                        @endif
                                    </td>
                                @endif
                            @endforeach
                        </tr>
                    @endforeach
                    
                    @if(!empty($totalsData))
                        @php
                            $totalRow = reset($totalsData);
                        @endphp
                        <tr class="total-row">
                            @foreach($headers as $header)
                                @if(!in_array($header, ['id', 'soc_building_id', 'unit_id', 'maintainance_due', 'ledger_balance']))
                                    <td class="{{ in_array($header, ['maintenance_due', 'indental_due', 'cr_bal', 'ledger_bal']) ? 'text-right' : 'text-center' }}">
                                        @if($header === 'building_unit_name')
                                            <strong>Total</strong>
                                        @elseif($header === 'maintenance_due' && isset($totalRow['maintenance_due_sum']))
                                            <strong>{{ number_format($totalRow['maintenance_due_sum'], 2) }}</strong>
                                        @elseif($header === 'indental_due' && isset($totalRow['incidental_due_sum']))
                                            <strong>{{ number_format($totalRow['incidental_due_sum'], 2) }}</strong>
                                        @elseif($header === 'cr_bal' && isset($totalRow['cr_bal_sum']))
                                            <strong>{{ number_format($totalRow['cr_bal_sum'], 2) }}</strong>
                                        @elseif($header === 'ledger_bal' && isset($totalRow['ledger_balance_sum']))
                                            <strong>{{ number_format($totalRow['ledger_balance_sum'], 2) }}</strong>
                                        @else

                                        @endif
                                    </td>
                                @endif
                            @endforeach
                        </tr>
                    @endif
                </tbody>
            </table>
            @endif
        @endif
    </div>
    
    @if(isset($data) && is_array($data) && count($data) > 1 && !empty($data[1]))
        @php
            $summaryData = reset($data[1]);
        @endphp
        <div class="summary-section">
            <div class="summary-title">Summary</div>
            <table class="summary-table">
                <thead>
                    <tr>
                        @if(isset($summaryData['maintenance_due_sum']))
                        <th class="text-center">Maintenance Dues</th>
                        @endif
                        @if(isset($summaryData['ledger_balance_sum']))
                        <th class="text-center">Ledger Balance</th>
                        @endif
                        @if(isset($summaryData['incidental_due_sum']))
                        <th class="text-center">Incidental Dues</th>
                        @endif
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        @if(isset($summaryData['maintenance_due_sum']))
                        <td class="text-center">{{ number_format($summaryData['maintenance_due_sum'], 2) }}</td>
                        @endif
                        @if(isset($summaryData['ledger_balance_sum']))
                        <td class="text-center">{{ number_format($summaryData['ledger_balance_sum'], 2) }}</td>
                        @endif
                        @if(isset($summaryData['incidental_due_sum']))
                        <td class="text-center">{{ number_format($summaryData['incidental_due_sum'], 2) }}</td>
                        @endif
                    </tr>
                </tbody>
            </table>
        </div>
    @endif
    
    @if(isset($report_date))
    <div class="report-date">
        Report date: {{ $report_date }}
    </div>
    @endif
    
    <div class="footer">
        <span>Powered By:</span> <img src="https://stgsociety.chsone.in/images/logo.png" alt="CHSONE" style="vertical-align: middle;">
    </div>
</body>
</html>
