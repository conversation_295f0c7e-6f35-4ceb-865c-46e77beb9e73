<html>
    <head>
        <title>Receipt</title>
        <style>
            .container{
                width: 100%;
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
                border: 1px solid #ccc;
            }
            .header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-top: 5%;
            }

            .left {
                width: 50%;
            }

            .right {
                width: 50%;
            }

            .left h1 {
                font-size: 20px;
                font-weight: 600;
                margin: 0;
                margin-bottom: 5px;
            }

            .left p {
                font-size: 12px;
                margin: 0;
            }

            .right h1 {
                font-size: 20px;
                font-weight: 600;
                margin: 0;
                margin-bottom: 5px;
            }

            .right p {
                font-size: 14px;
                margin: 0;
            }

            .right p span {
                font-weight: 600;
            }

            .table {
                margin-top: 5%;
            }

            .table table {
                width: 95%;
                border-collapse: collapse;
            }

            .table table tr td {
                font-size: 14px;
                padding: 10px;
            }

            .table table tr td:first-child {
                width: 15%;
            }

            .table table tr td:last-child {
                width: 85%;
                border-bottom: 1px solid #111111;
            }

            .table table tr td span {
                font-weight: 600;
            }

            .payment-mode {
                margin-top: 5%;
            }

            .payment-mode h1 {
                font-size: 20px;
                font-weight: 600;
                margin: 0;
                margin-bottom: 5px;
            }

            .payment-table {
                width: 30%;
                border-collapse: collapse;
                margin-top: 20px;
            }

            .payment-table tr td {
                font-size: 14px;
                padding: 10px;
            }

            .payment-table tr td:first-child {
                width: 50%;
            }

            .payment-table tr td:last-child {
                width: 50%;
                border: 1px solid #111111;
            }

            .icon {
                width: 24px; /* Set the width and height for the icon container */
                height: 24px;
                display: inline-block;
                position: relative;
            }

            /* Define the checkmark icon */
            .checkmark::before {
                content: '\2713'; /* Unicode for a checkmark symbol */
                font-size: 24px;
                color: #111111; /* Icon color */
                position: absolute;
                top: 0;
                left: 40;
            }

            .footer {
                margin-top: 5%;
                text-align: right;
                margin-right: 5%;
            }

            .footer h1 {
                font-size: 16px;
                font-weight: 600;
                margin: 0;
                margin-bottom: 5px;
            }

            .footer p {
                font-size: 14px;
                margin: 35px 0 0 0;
            }

            .note {
                margin-top: 5%;
            }

            .note p {
                font-size: 14px;
                margin: 0;
                font-weight: 600;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="left">
                    <h1>Demo Cube One</h1>
                    <p>Reg No: AKKI1234567890Phase 2, Kphb Colony,</p>
                    <p>Near Railway Station, </p>
                    <p>Mumbai, Maharashtra, 400703.</p>
                </div>

                <div class="right">
                    <h1>PAYMENT RECEIPT</h1>
                    <p><span>Receipt No:</span> # REC00184 (original)</p>
                    <p><span>Receipt Date:</span> Feb 20, 2023</p>
                </div>
            </div>

            <div class="table">
                <table>
                    <tr>
                        <td>Received from</td>
                        <td><span>Mr. John Doe</span></td>
                    </tr>
                    <tr>
                        <td>Amount (Rs.)</td>
                        <td><span>1662</span> (One Thousand Six Hundred Sixty Two Rupees)</td>
                    </tr>
                    <tr>
                        <td>For</td>
                        <td>Maintenance Invoice of Building 1/SF101 : INV2021_00395(Test cash payment received )</td>
                    </tr>
                </table>
            </div>

            <div class="payment-mode">
                <h1>Payment Received in:</h1>

                <table class="payment-table">
                    <tr>
                        <td>Cash</td>
                        <td>
                            <div class="icon checkmark"></div>
                        </td>
                    </tr>
                    <tr>
                        <td>Cheque</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>Electronic Fund Transfer</td>
                        <td></td>
                    </tr>
                </table>
            </div>

            <div class="footer">
                <h1>Demo Cube One</h1>
                <p>Received By</p>
            </div>

            <div class="note">
                <p><span>Note:</span> This is a computer generated receipt and does not require any signature.</p>
            </div>

        </div>
    </body>
</html>