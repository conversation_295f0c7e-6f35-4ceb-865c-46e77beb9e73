<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CHSONE</title>
    <link rel="shortcut icon" href="https://stgsociety.chsone.in/images/favicon.png" />
    <style>
        html { font-size: 62.5%; margin: 0; }
        body { font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; font-size: 12px; margin: 0; color: #333; background: #fff; }
        table { background: white; border: solid 1px #ddd; width: 100%; margin: 20px 0; border-collapse: collapse; }
        table th, table td { padding: 8px; border: 1px solid #ddd; text-align: left; }
        table thead { background-color: #f0f0f0; }
        .printOption { background: #001941; height: 40px; padding: 10px; color: #fff; }
        .footer { background: #001941; padding: 10px; color: #fff; text-align: right; }
    </style>
</head>
<body>
    <div class="printOption">
        <p>FUTURESCAPE TECHNOLOGIES PVT LTD</p>
    </div>

    <div class="tableDiv" style="width: 98%; margin: 0 auto;">
        <table id="myTable05">
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Tag No</th>
                    <th>Vendor Name</th>
                    <th>Category</th>
                    <th>Location</th>
                    <th>Purchase Date</th>
                    <th>Asset Cost</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                @forelse($data as $index => $account)
                <tr>
                    <td>{{ $account['assets_name'] ?? '-' }}</td>
                    <td>{{ $account['assets_tag_number'] ?? '-' }}</td>
                    <td>{{ $account['vendor_name'] ?? '-' }}</td>
                    <td>{{ $account['assets_categories_name'] ?? '-' }}</td>
                    <td>{{ $account['assets_location'] ?? '-' }}</td>
                    <td>{{ $account['assets_purchase_date'] ?? '-' }}</td>
                    <td>{{ $account['assets_cost'] ?? '-' }}</td>
                    <td>{{ $account['status'] ?? '-' }}</td>
                </tr>
                @empty
                <tr>
                    <td colspan="6" style="text-align: center;">No records found</td>
                </tr>
                @endforelse
            </tbody>
        </table>
    </div>

    <div class="footer">
        <span>Powered By:</span> <img src="https://stgsociety.chsone.in/images/logo.png" alt="CHSONE" style="vertical-align: middle;">
    </div>
</body>
</html>
