{"codeToName": {"32": "space", "160": "space", "33": "exclam", "34": "quotedbl", "35": "numbersign", "36": "dollar", "37": "percent", "38": "ampersand", "146": "quoteright", "40": "parenleft", "41": "parenright", "42": "asterisk", "43": "plus", "44": "comma", "45": "hyphen", "173": "hyphen", "46": "period", "47": "slash", "48": "zero", "49": "one", "50": "two", "51": "three", "52": "four", "53": "five", "54": "six", "55": "seven", "56": "eight", "57": "nine", "58": "colon", "59": "semicolon", "60": "less", "61": "equal", "62": "greater", "63": "question", "64": "at", "65": "A", "66": "B", "67": "C", "68": "D", "69": "E", "70": "F", "71": "G", "72": "H", "73": "I", "74": "J", "75": "K", "76": "L", "77": "M", "78": "N", "79": "O", "80": "P", "81": "Q", "82": "R", "83": "S", "84": "T", "85": "U", "86": "V", "87": "W", "88": "X", "89": "Y", "90": "Z", "91": "bracketleft", "92": "backslash", "93": "bracketright", "94": "asciicircum", "95": "underscore", "145": "quoteleft", "97": "a", "98": "b", "99": "c", "100": "d", "101": "e", "102": "f", "103": "g", "104": "h", "105": "i", "106": "j", "107": "k", "108": "l", "109": "m", "110": "n", "111": "o", "112": "p", "113": "q", "114": "r", "115": "s", "116": "t", "117": "u", "118": "v", "119": "w", "120": "x", "121": "y", "122": "z", "123": "braceleft", "124": "bar", "125": "braceright", "126": "asciitilde", "161": "exclamdown", "162": "cent", "163": "sterling", "165": "yen", "131": "florin", "167": "section", "164": "currency", "39": "<PERSON><PERSON><PERSON>", "147": "quotedblleft", "171": "guillemotleft", "139": "guil<PERSON>lle<PERSON>", "155": "guil<PERSON><PERSON><PERSON>", "150": "endash", "134": "dagger", "135": "daggerdbl", "183": "periodcentered", "182": "paragraph", "149": "bullet", "130": "quotesinglbase", "132": "quotedblbase", "148": "<PERSON><PERSON><PERSON><PERSON>", "187": "guil<PERSON><PERSON><PERSON>", "133": "ellipsis", "137": "perth<PERSON>and", "191": "questiondown", "96": "grave", "180": "acute", "136": "circumflex", "152": "tilde", "175": "macron", "168": "<PERSON><PERSON><PERSON>", "184": "cedilla", "151": "emdash", "198": "AE", "170": "ordfeminine", "216": "<PERSON><PERSON><PERSON>", "140": "OE", "186": "ordmasculine", "230": "ae", "248": "oslash", "156": "oe", "223": "germandbls", "207": "Idieresis", "233": "eacute", "159": "Ydieresis", "247": "divide", "221": "Ya<PERSON>", "194": "Acircumflex", "225": "aacute", "219": "Ucircumflex", "253": "yacute", "234": "ecircumflex", "220": "Udieresis", "218": "Uacute", "203": "Edieresis", "169": "copyright", "229": "aring", "224": "agrave", "227": "atilde", "154": "scaron", "237": "iacute", "251": "ucircumflex", "226": "acircumflex", "231": "ccedilla", "222": "Thorn", "179": "threesuperior", "210": "<PERSON><PERSON>", "192": "<PERSON><PERSON>", "215": "multiply", "250": "uacute", "255": "ydieresis", "238": "icircumflex", "202": "Ecircumflex", "228": "adieresis", "235": "edieresis", "205": "Iacute", "177": "plus<PERSON>us", "166": "brokenbar", "174": "registered", "200": "<PERSON><PERSON>", "142": "<PERSON><PERSON><PERSON>", "208": "Eth", "199": "Ccedilla", "193": "Aacute", "196": "Adieresis", "232": "egrave", "211": "Oacute", "243": "oacute", "239": "idieresis", "212": "Ocircumflex", "217": "<PERSON><PERSON>", "254": "thorn", "178": "twosuperior", "214": "Odieresis", "181": "mu", "236": "igrave", "190": "threequarters", "153": "trademark", "204": "<PERSON><PERSON>", "189": "onehalf", "244": "ocircumflex", "241": "ntilde", "201": "Eacute", "188": "onequarter", "138": "<PERSON><PERSON><PERSON>", "176": "degree", "242": "ograve", "249": "ugrave", "209": "Ntilde", "245": "otilde", "195": "<PERSON><PERSON>", "197": "<PERSON><PERSON>", "213": "<PERSON><PERSON><PERSON>", "206": "Icircumflex", "172": "logicalnot", "246": "odieresis", "252": "udieresis", "240": "eth", "158": "z<PERSON>on", "185": "onesuperior", "128": "Euro"}, "isUnicode": false, "FontName": "Courier-Bold", "FullName": "Courier Bold", "FamilyName": "Courier", "Weight": "Bold", "ItalicAngle": "0", "IsFixedPitch": "true", "CharacterSet": "ExtendedRoman", "FontBBox": ["-113", "-250", "749", "801"], "UnderlinePosition": "-100", "UnderlineThickness": "50", "Version": "003.000", "EncodingScheme": "WinAnsiEncoding", "CapHeight": "562", "XHeight": "439", "Ascender": "629", "Descender": "-157", "StdHW": "84", "StdVW": "106", "StartCharMetrics": "317", "C": {"32": 600, "160": 600, "33": 600, "34": 600, "35": 600, "36": 600, "37": 600, "38": 600, "146": 600, "40": 600, "41": 600, "42": 600, "43": 600, "44": 600, "45": 600, "173": 600, "46": 600, "47": 600, "48": 600, "49": 600, "50": 600, "51": 600, "52": 600, "53": 600, "54": 600, "55": 600, "56": 600, "57": 600, "58": 600, "59": 600, "60": 600, "61": 600, "62": 600, "63": 600, "64": 600, "65": 600, "66": 600, "67": 600, "68": 600, "69": 600, "70": 600, "71": 600, "72": 600, "73": 600, "74": 600, "75": 600, "76": 600, "77": 600, "78": 600, "79": 600, "80": 600, "81": 600, "82": 600, "83": 600, "84": 600, "85": 600, "86": 600, "87": 600, "88": 600, "89": 600, "90": 600, "91": 600, "92": 600, "93": 600, "94": 600, "95": 600, "145": 600, "97": 600, "98": 600, "99": 600, "100": 600, "101": 600, "102": 600, "103": 600, "104": 600, "105": 600, "106": 600, "107": 600, "108": 600, "109": 600, "110": 600, "111": 600, "112": 600, "113": 600, "114": 600, "115": 600, "116": 600, "117": 600, "118": 600, "119": 600, "120": 600, "121": 600, "122": 600, "123": 600, "124": 600, "125": 600, "126": 600, "161": 600, "162": 600, "163": 600, "fraction": 600, "165": 600, "131": 600, "167": 600, "164": 600, "39": 600, "147": 600, "171": 600, "139": 600, "155": 600, "fi": 600, "fl": 600, "150": 600, "134": 600, "135": 600, "183": 600, "182": 600, "149": 600, "130": 600, "132": 600, "148": 600, "187": 600, "133": 600, "137": 600, "191": 600, "96": 600, "180": 600, "136": 600, "152": 600, "175": 600, "breve": 600, "dotaccent": 600, "168": 600, "ring": 600, "184": 600, "hungarumlaut": 600, "ogonek": 600, "caron": 600, "151": 600, "198": 600, "170": 600, "Lslash": 600, "216": 600, "140": 600, "186": 600, "230": 600, "dotlessi": 600, "lslash": 600, "248": 600, "156": 600, "223": 600, "207": 600, "233": 600, "abreve": 600, "uhungarumlaut": 600, "ecaron": 600, "159": 600, "247": 600, "221": 600, "194": 600, "225": 600, "219": 600, "253": 600, "scommaaccent": 600, "234": 600, "Uring": 600, "220": 600, "aogonek": 600, "218": 600, "uogonek": 600, "203": 600, "Dcroat": 600, "commaaccent": 600, "169": 600, "Emacron": 600, "ccaron": 600, "229": 600, "Ncommaaccent": 600, "lacute": 600, "224": 600, "Tcommaaccent": 600, "Cacute": 600, "227": 600, "Edotaccent": 600, "154": 600, "scedilla": 600, "237": 600, "lozenge": 600, "Rcaron": 600, "Gcommaaccent": 600, "251": 600, "226": 600, "Amacron": 600, "rcaron": 600, "231": 600, "Zdotaccent": 600, "222": 600, "Omacron": 600, "Racute": 600, "Sacute": 600, "dcaron": 600, "Umacron": 600, "uring": 600, "179": 600, "210": 600, "192": 600, "Abreve": 600, "215": 600, "250": 600, "Tcaron": 600, "partialdiff": 600, "255": 600, "Nacute": 600, "238": 600, "202": 600, "228": 600, "235": 600, "cacute": 600, "nacute": 600, "umacron": 600, "Ncaron": 600, "205": 600, "177": 600, "166": 600, "174": 600, "Gbreve": 600, "Idotaccent": 600, "summation": 600, "200": 600, "racute": 600, "omacron": 600, "Zacute": 600, "142": 600, "greaterequal": 600, "208": 600, "199": 600, "lcommaaccent": 600, "tcaron": 600, "eogonek": 600, "Uogonek": 600, "193": 600, "196": 600, "232": 600, "zacute": 600, "iogonek": 600, "211": 600, "243": 600, "amacron": 600, "sacute": 600, "239": 600, "212": 600, "217": 600, "Delta": 600, "254": 600, "178": 600, "214": 600, "181": 600, "236": 600, "ohungarumlaut": 600, "Eogonek": 600, "dcroat": 600, "190": 600, "Scedilla": 600, "lcaron": 600, "Kcommaaccent": 600, "Lacute": 600, "153": 600, "edotaccent": 600, "204": 600, "Imacron": 600, "Lcaron": 600, "189": 600, "lessequal": 600, "244": 600, "241": 600, "Uhungarumlaut": 600, "201": 600, "emacron": 600, "gbreve": 600, "188": 600, "138": 600, "Scommaaccent": 600, "Ohungarumlaut": 600, "176": 600, "242": 600, "Ccaron": 600, "249": 600, "radical": 600, "Dcaron": 600, "rcommaaccent": 600, "209": 600, "245": 600, "Rcommaaccent": 600, "Lcommaaccent": 600, "195": 600, "Aogonek": 600, "197": 600, "213": 600, "zdotaccent": 600, "Ecaron": 600, "Iogonek": 600, "kcommaaccent": 600, "minus": 600, "206": 600, "ncaron": 600, "tcommaaccent": 600, "172": 600, "246": 600, "252": 600, "notequal": 600, "gcommaaccent": 600, "240": 600, "158": 600, "ncommaaccent": 600, "185": 600, "imacron": 600, "128": 600}, "CIDtoGID_Compressed": true, "CIDtoGID": "eJwDAAAAAAE=", "_version_": 6}