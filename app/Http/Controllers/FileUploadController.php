<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Helpers\S3Helper;

class FileUploadController extends Controller
{
    public function fileUpload(Request $request)
    {
        // get all the headers and convert them to request parameters
        $headers = $request->header();
        foreach($headers as $key => $value){
            $request[$key] = $value[0];
        }

        // make a helper function for this
        $s3Response = S3Helper::uploadToS3($request);
        $s3Response = $s3Response->getData();
        
        if($s3Response->status == 200){
            return response()->json([
                'data' => $s3Response->data,
                'message' => 'success',
                'status' => 200
            ]);
        }else{
            return response()->json([
                'message' => 'There was an error uploading the file.',
                'error' => $s3Response->error,
                'status' => 500
            ]);
        }
        
    }
}
