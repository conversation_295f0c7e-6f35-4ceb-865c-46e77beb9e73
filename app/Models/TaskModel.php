<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TaskModel extends Model
{
    use HasFactory;

    protected $table = 'tasks';

    protected $fillable = [
        'soc_id',
        'title',
        'description',
        'status',
        'created_at',
        'updated_at',
    ];

    public function jobs()
    {
        return $this->hasMany(JobModel::class, 'task_id', 'id');
    }
}
