<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class JobModelFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'task_id' => fake()->numberBetween(1, 10),
            'user_id' => fake()->numberBetween(1, 10),
            'email_or_phone' => fake()->unique()->safeEmail(),
            'type' => fake()->randomElement(['email', 'sms']),
            'subject' => fake()->sentence(),
            'content' => fake()->paragraph(),
            'url' => fake()->url(),
            'cc_users' => fake()->unique()->safeEmail(),
            'bcc_users' => fake()->unique()->safeEmail(),
            'status' => fake()->randomElement([0, 1]),
            'is_failed' => fake()->randomElement([0, 1]),
            'failed_reason' => fake()->sentence(),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
