<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

Route::post('/generate-pdf', 'App\Http\Controllers\PDFGeneratorController@generatePDF');
Route::post('/generate-excel', 'App\Http\Controllers\PDFGeneratorController@generateExcel');
Route::post('/download-PDF', 'App\Http\Controllers\PDFGeneratorController@downloadPDF');
Route::post('/download-noc', 'App\Http\Controllers\PDFGeneratorController@downloadNoc');
Route::post('/download-notice', 'App\Http\Controllers\PDFGeneratorController@downloadNotice');


Route::post('/send-mail', 'App\Http\Controllers\MailController@sendMail');

Route::post('/file-upload', 'App\Http\Controllers\FileUploadController@fileUpload');

Route::post('/society/mail', 'App\Http\Controllers\SocietyController@sendMail');