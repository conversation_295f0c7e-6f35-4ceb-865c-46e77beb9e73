<?php

namespace App\Requests;

use Illuminate\Foundation\Http\FormRequest;

class InvoiceGenerationRequest extends FormRequest
{
    // public function authorize()
    // {
    //     return true;
    // }

    public function rules()
    {
        return [
            'service_id' => 'required|integer',
            'company_id' => 'required|integer',
            'user_id' => 'required|integer',
            'template' => 'required',
            'file_name' => 'required|string',
            'file_type' => 'required|string',
        ];
    }
}