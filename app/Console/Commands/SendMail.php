<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class SendMail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sendmail {--email=} {--subject=} {--view=} {--attachment=*} {--name=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send mail to user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->option('email');
        $subject = $this->option('subject');
        $view = $this->option('view');
        $attachments = $this->option('attachment');
        $attachments = is_array($attachments) ? $attachments : [$attachments];
        $name = $this->option('name');

        $mail = Mail::to($email)->send(new \App\Mail\SendMail($subject, $view, $attachments));
        
        if ($mail) {
            $this->info('Mail sent successfully');
        } else {
            $this->error('Mail not sent');
        }
    }
}
