variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: ""

stages:
  - deploy

deploy:
  stage: deploy
  image: ubuntu:22.04
  before_script:
    - apt-get update -y
    - apt install -y openssh-client
    - chmod 0400 $AWS_PEM_FILE
    - ls -la
  script:
    - chmod 0400 $AWS_PEM_FILE
    - ssh -oStrictHostKeyChecking=no -o "IdentitiesOnly yes" -T -i $AWS_PEM_FILE ubuntu@************** "cd /home/<USER>/society-micro-services/ && sudo git pull ${CI_REPOSITORY_URL} && sudo docker compose up --build -d && sudo docker ps"
  only:
    - main
