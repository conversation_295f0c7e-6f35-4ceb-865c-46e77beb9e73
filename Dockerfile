FROM php:8.1-fpm-alpine3.18 AS builder

# Update system packages and install build dependencies
RUN apk update && apk upgrade && apk add --no-cache --quiet \
    autoconf \
    git \
    build-base \
    zip \
    oniguruma-dev \
    libzip-dev \
    libpng-dev \
    vim \
    curl \
    gmp-dev \
    openssl-dev

RUN docker-php-ext-install gd pdo_mysql mbstring zip exif pcntl

RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

WORKDIR /var/www/html

COPY . .

# Set permissions for Laravel
#RUN chown -R www-data:www-data /var/www/html \
#    && chmod -R 755 /var/www/html/storage

RUN composer install --no-scripts --no-autoloader

# RUN composer update

RUN composer dump-autoload --optimize

RUN chmod +x artisan

CMD php artisan serve --host 0.0.0.0 --port 8011

EXPOSE 8011
