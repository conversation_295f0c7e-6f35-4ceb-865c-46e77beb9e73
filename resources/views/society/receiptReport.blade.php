<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $society_name ?? 'Society Report' }}</title>
    <link rel="shortcut icon" href="https://stgsociety.chsone.in/images/favicon.png" />
    <style>
        html { font-size: 62.5%; margin: 0; }
        body { font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; font-size: 12px; margin: 0; color: #333; background: #fff; }
        table { background: white; border: solid 1px #ddd; width: 100%; margin: 20px 0; border-collapse: collapse; }
        table th, table td { padding: 6px; border: 1px solid #ddd; text-align: left; font-size: 11px; }
        table thead { background-color: #f0f0f0; }
        .printOption { background: #001941; height: 40px; padding: 10px; color: #fff; }
        .footer { background: #001941; padding: 10px; color: #fff; text-align: right; }
        .section-header { font-weight: bold; text-align: left; background: #f9f9f9; font-size: 1.1em; }
        .main-title { text-align: center; font-size: 1.6em; font-weight: bold; margin-top: 20px; }
        .sub-title { text-align: center; font-size: 1.2em; margin-bottom: 20px; }
        .report-info { text-align: center; margin-bottom: 20px; }
        .report-info div { margin: 5px 0; }
        .summary-section { margin-top: 30px; }
        .summary-title { font-weight: bold; font-size: 1.3em; margin-bottom: 15px; }
        .summary-table { background-color: #f9f9f9; }
        .summary-table th, .summary-table td { background-color: #f9f9f9; font-weight: bold; }
        .total-row { background-color: #e9e9e9; font-weight: bold; }
        .report-date { text-align: left; margin-top: 20px; font-weight: bold; }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        .small-font { font-size: 10px; }
    </style>
</head>
<body>
    <div class="printOption">
        <p>{{ 'Receipt Report' ?? 'Society Report' }}</p>
    </div>
    
    <div class="main-title">{{ $company_details['society_name'] ?? 'SOCIETY NAME' }}</div>
    
    @if(isset($reg_number) || isset($gstin))
    <div class="report-info">
        @if(isset($reg_number))
        <div>Reg No: {{ $reg_number }}</div>
        @endif
        @if(isset($gstin))
        <div>GSTIN/UIN: {{ $gstin }}</div>
        @endif
    </div>
    @endif
    
    @if(isset($report_title))
    <div class="sub-title">{{ $report_title }}</div>
    @endif
    
    @if(isset($date_range))
    <div class="text-center">
        <strong>Date: {{ $date_range }}</strong>
    </div>
    @endif
    
    <div class="tableDiv" style="width: 98%; margin: 0 auto;">
        @if(isset($data) && is_array($data) && count($data) > 0)
            @php
                $reportData = $data[0] ?? [];
                $totalsData = $data[1] ?? [];
                
                // Define custom order for columns
                $orderedHeaders = [
                    'receipt_number',
                    'payment_date',
                    'society_unit_name',
                    'received_from',
                    'invoice_number',
                    'bill_type',
                    'payment_mode',
                    'transaction_reference',
                    'payment_instrument',
                    'total_due_amount',
                    'late_payment_charges',
                    'payment_amount',
                    'writeoff_amount',
                    'tds_deducted',
                    'transaction_status',
                    'payment_note'
                ];
                
                // Get headers from first row and order them
                $headers = [];
                if (!empty($reportData)) {
                    $firstRow = reset($reportData);
                    if (is_array($firstRow)) {
                        $allHeaders = array_keys($firstRow);
                        
                        // Filter and order headers based on what exists in data
                        foreach ($orderedHeaders as $orderedHeader) {
                            if (in_array($orderedHeader, $allHeaders)) {
                                $headers[] = $orderedHeader;
                            }
                        }
                        
                        // Add any remaining headers that weren't in our ordered list
                        foreach ($allHeaders as $header) {
                            if (!in_array($header, $headers) && !in_array($header, ['id', 'soc_id', 'unit_id', 'other_information', 'payment_token', 'reversal_note', 'cheque_date', 'attachment_name', 'updated_by', 'updated_date', 'created_by', 'created_date', 'soc_building_name', 'unit_flat_number'])) {
                                $headers[] = $header;
                            }
                        }
                    }
                }
            @endphp
            
            @if(!empty($headers) && !empty($reportData))
            <table>
                <thead>
                    <tr>
                        @foreach($headers as $header)
                            <th class="small-font">
                                @switch($header)
                                    @case('receipt_number')
                                        Receipt Number
                                        @break
                                    @case('payment_date')
                                        Payment Date
                                        @break
                                    @case('society_unit_name')
                                        Unit
                                        @break
                                    @case('received_from')
                                        Received From
                                        @break
                                    @case('invoice_number')
                                        Invoice Number
                                        @break
                                    @case('bill_type')
                                        Bill Type
                                        @break
                                    @case('payment_mode')
                                        Payment Mode
                                        @break
                                    @case('transaction_reference')
                                        Transaction Reference
                                        @break
                                    @case('payment_instrument')
                                        Payment Instrument
                                        @break
                                    @case('total_due_amount')
                                        Total Due Amount
                                        @break
                                    @case('late_payment_charges')
                                        Late Payment Charges
                                        @break
                                    @case('payment_amount')
                                        Payment Amount
                                        @break
                                    @case('writeoff_amount')
                                        Write-off Amount
                                        @break
                                    @case('tds_deducted')
                                        TDS Deducted
                                        @break
                                    @case('transaction_status')
                                        Transaction Status
                                        @break
                                    @case('payment_note')
                                        Payment Note
                                        @break
                                    @default
                                        {{ ucwords(str_replace('_', ' ', $header)) }}
                                @endswitch
                            </th>
                        @endforeach
                    </tr>
                </thead>
                <tbody>
                    @foreach($reportData as $row)
                        <tr>
                            @foreach($headers as $header)
                                <td class="{{ in_array($header, ['total_due_amount', 'late_payment_charges', 'payment_amount', 'writeoff_amount', 'tds_deducted', 'transaction_charges']) ? 'text-right small-font' : 'small-font' }}">
                                    @if(in_array($header, ['total_due_amount', 'late_payment_charges', 'payment_amount', 'writeoff_amount', 'tds_deducted', 'transaction_charges']) && is_numeric($row[$header]))
                                        {{ number_format($row[$header], 2) }}
                                    @elseif($header === 'payment_date' && $row[$header])
                                        {{ date('d/m/Y', strtotime($row[$header])) }}
                                    @elseif($header === 'bill_type')
                                        {{ ucfirst($row[$header] ?? '') }}
                                    @elseif($header === 'payment_mode')
                                        {{ ucfirst($row[$header] ?? '') }}
                                    @else
                                        {{ $row[$header] ?? '' }}
                                    @endif
                                </td>
                            @endforeach
                        </tr>
                    @endforeach
                </tbody>
            </table>
            @endif
        @endif
    </div>
    
    @if(isset($data) && is_array($data) && count($data) > 1 && !empty($data[1]))
        @php
            $summaryData = reset($data[1]);
        @endphp
        <div class="summary-section">
            <div class="summary-title">Summary</div>
            <table class="summary-table">
                <thead>
                    <tr>
                        @if(isset($summaryData['paid_amount_total']))
                        <th class="text-center">Total Paid Amount</th>
                        @endif
                        @if(isset($summaryData['tds_deducted_total']))
                        <th class="text-center">Total TDS Deducted</th>
                        @endif
                        @if(isset($summaryData['write_off_total']))
                        <th class="text-center">Total Write-off</th>
                        @endif
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        @if(isset($summaryData['paid_amount_total']))
                        <td class="text-center">{{ number_format($summaryData['paid_amount_total'], 2) }}</td>
                        @endif
                        @if(isset($summaryData['tds_deducted_total']))
                        <td class="text-center">{{ number_format($summaryData['tds_deducted_total'], 2) }}</td>
                        @endif
                        @if(isset($summaryData['write_off_total']))
                        <td class="text-center">{{ number_format($summaryData['write_off_total'], 2) }}</td>
                        @endif
                    </tr>
                </tbody>
            </table>
        </div>
    @endif
    
    @if(isset($report_date))
    <div class="report-date">
        Report date: {{ $report_date }}
    </div>
    @endif
    
    <div class="footer">
        <span>Powered By:</span> <img src="https://stgsociety.chsone.in/images/logo.png" alt="CHSONE" style="vertical-align: middle;">
    </div>
</body>
</html>
