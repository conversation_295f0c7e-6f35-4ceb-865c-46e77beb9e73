<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Helpers\MailHelper;

class MailController extends Controller
{
    public function sendMail(Request $request)
    {
        // $command = 'app:sendmail';
        
        // \Artisan::call($command, array (
        //     '--email' => $request->email,
        //     '--subject' => $request->subject,
        //     '--view' => $request->view,
        //     '--attachment' => $request->file('attachment'),
        //     '--name' => $request->name,
        // ));

        // $output = \Artisan::output();

        // return response()->json([
        //     'from' => $request->from,
        //     'to' => $request->to,
        //     'cc' => $request->cc,
        //     'bcc' => $request->bcc,
        //     'subject' => $request->subject,
        //     'content_message' => $request->content_message,
        //     'encoding' => $request->encoding,
        //     'files' => $request->files,
        //     'status' => 200
        // ]);


        $response = MailHelper::send_phpmailer(
            $request->from,
            $request->toMyData,
            $request->cc,
            $request->bcc,
            $request->subject,
            $request->content_message,
            $request->encoding,
            $request->fileData);


        return response()->json([
            'message' => 'Mail sent successfully',
            'output' => $response,
            'request' => $request->all(),
            'status' => 200
        ]);
    }
}
