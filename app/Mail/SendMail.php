<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class SendMail extends Mailable
{
    use Queueable, SerializesModels;

    public $subject;
    public $view;
    public $attachments;
    public $content;
    /**
     * Create a new message instance.
     */
    public function __construct($subject, $view, $attachments=[], $content)
    {
        $this->subject = $subject;
        $this->view = $view;
        $this->attachments = $attachments;
        $this->content = $content;
    }

    /**
     * Build the message.
     */

    public function build(){

        $mail = $this->view('email.'.$this->view)
            ->subject($this->subject)
            ->with([
                'content' => $this->content,
            ]);

        if ($this->attachments) {
            // foreach ($this->attachments as $attachment) {
            //     if($attachment){
            //         $mail->attach($attachment->getRealPath(), [
            //             'as' => $attachment->getClientOriginalName(),
            //             'mime' => $attachment->getMimeType(),
            //         ]);
            //     }
            // }

            // my attachments is a s3 url
            foreach ($this->attachments as $attachment) {
                if($attachment){
                    $mail->attach($attachment);
                }
            }
        }

        // remove 0 index from attachments $mail->attach
        unset($mail->attachments[0]);

        return $mail;
    }
}
