<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $society_name ?? 'Parking Allotment Detail Report' }}</title>
    <link rel="shortcut icon" href="https://stgsociety.chsone.in/images/favicon.png" />
    <style>
        html { font-size: 62.5%; margin: 0; }
        body { font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; font-size: 12px; margin: 0; color: #333; background: #fff; }
        table { background: white; border: solid 1px #ddd; width: 100%; margin: 20px 0; border-collapse: collapse; }
        table th, table td { padding: 6px; border: 1px solid #ddd; text-align: left; font-size: 11px; }
        table thead { background-color: #f0f0f0; }
        .printOption { background: #001941; height: 40px; padding: 10px; color: #fff; }
        .footer { background: #001941; padding: 10px; color: #fff; text-align: right; }
        .main-title { text-align: center; font-size: 1.6em; font-weight: bold; margin-top: 20px; }
        .sub-title { text-align: center; font-size: 1.2em; margin-bottom: 20px; color: #666; }
        .report-date { text-align: left; margin-top: 20px; font-weight: bold; }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        .small-font { font-size: 10px; }
        .status-allotted { color: blue; }
        .status-unallotted { color: red; }
    </style>
</head>
<body>
    <div class="printOption">
        <p>{{ 'Parking Allotment Detail Report' ?? 'Society Report' }}</p>
    </div>

    <div style="width:100%;text-align:center;padding:10px;">
        <h2 style="margin-bottom:0;margin-top:5px">{{$company_details['society_name'] ?? 'River Walk CHS'}}</h2>
        <h3 style="margin-bottom:0;margin-top:5px">Reg No: {{$company_details['society_reg_num'] ?? 'AKKL123456789'}} | GSTIN/UIN: {{$company_details['gstin'] ?? '27TGDCBA1234G'}}</h3>
        <p style="margin-bottom:0;margin-top:5px">{{$company_details['society_address'] ?? 'Society Address'}}</p>
    </div>

    <div class="sub-title">Parking Allotment Detail</div>

    <div class="text-center">
        <strong>Filter: Allotted - All</strong>
    </div>

    <div class="tableDiv" style="width: 98%; margin: 0 auto;">
        @if(isset($data) && is_array($data) && count($data) > 0)
            <table>
                <thead>
                    <tr>
                        <th class="small-font">Parking Unit</th>
                        <th class="small-font">Parking Type</th>
                        <th class="small-font">Parking For</th>
                        <th class="small-font">Allotted To</th>
                        <th class="small-font">Primary Member</th>
                        <th class="small-font">Status</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($data as $row)
                        <tr>
                            <td class="small-font">{{ $row['parking_unit_name'] ?? $row['parking_number'] ?? '' }}</td>
                            <td class="small-font">{{ ucfirst($row['unit_parking_type'] ?? 'Parking') }}</td>
                            <td class="small-font">{{ $row['parking_type'] ?? '' }}</td>
                            <td class="small-font">{{ $row['allotment_to'] ?? '' }}</td>
                            <td class="small-font">{{ trim($row['member_primary_name'] ?? '') }}</td>
                            <td class="small-font">
                                {{ ucfirst($row['status'] ?? '') }}
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        @else
            <div class="text-center" style="padding: 20px;">
                <p>No data available for the selected criteria.</p>
            </div>
        @endif
    </div>

    <div class="report-date">
        Report date: {{ $report_date ?? date('d/m/Y') }}
    </div>

    <div class="footer">
        <span>Powered By:</span> <img src="https://stgsociety.chsone.in/images/logo.png" alt="CHSONE" style="vertical-align: middle;">
    </div>
</body>
</html>