My rhis blade sucessfully generates pdf with following data

array:21 [ // app/Console/Commands/Workflows/Income/PreviewWithGenerateDownloadWorkflow.php:102
  "arrAllUnitInvoiceDetail" => array:1 [
    3 => array:31 [
      "error" => false
      "showBankDetail" => 0
      "invoiceFontSize" => 9
      "showBankWithoutTaxParitcular" => 2
      "invoiceTaxAmount" => 0
      "arrLateChargesDetail" => array:2 [
        0 => array:16 [
          "id" => 1
          "simple_interest" => 21
          "grace_period" => 62
          "grace_duration" => "62"
          "effective_date" => "2021-03-31T18:30:00.000000Z"
          "calculate_from" => "billdate"
          "calculate_for" => "perday"
          "created_date" => "2022-01-21T12:03:28.000000Z"
          "created_by" => 1066
          "updated_date" => "2025-01-13T06:50:06.000000Z"
          "updated_by" => 1066
          "soc_id" => 8485
          "interest_amount_type" => "percentage"
          "interest_type" => "simple_interest"
          "applicable_taxes" => ""
          "type" => "maintenance"
        ]
        "interest_text" => "% per year"
      ]
      "originalColumnCount" => 2
      "defaultColumnCount" => 2
      "arrTaxDetail" => ""
      "arrLateChargeTaxClass" => ""
      "arrAppliedTaxDetail" => ""
      "arrAppliedTaxCategory" => ""
      "arrTaxCategoryParticular" => ""
      "arrTaxCategoryParticularSubtotal" => ""
      "lateChargeTotalTax" => ""
      "arrAllAppliedTaxDetail" => []
      "invoiceTotalAmount" => 13048
      "grandTotalAmount" => 13048
      "outstandingPrincipalAmount" => 0
      "outstandingInterestAmount" => 0
      "interestAmount" => 649
      "advanceAmount" => 0
      "associateMembers" => ""
      "balanceDue" => 13048
      "rupeesInWord" => "Thirteen Thousand Forty Eight Rupees "
      "arrIncomeInvoiceDetail" => array:1 [
        0 => array:25 [
          "soc_id" => "8485"
          "unit_id" => "3"
          "interest_amount" => 649
          "principal_amount" => 12399
          "advance_amount" => 0
          "roundoff_amount" => 0
          "start_date" => "2025-07-01"
          "end_date" => "2025-09-30"
          "bill_date" => "2025-07-01"
          "due_date" => "31-08-2025"
          "arrUnitData" => array:17 [
            "unit_id" => "3"
            "unit_category" => "1151"
            "ledger_account_id" => 105
            "fk_unit_category_id" => 3
            "soc_building_id" => 1
            "soc_building_name" => "Flat"
            "soc_building_floor" => 1
            "unit_flat_number" => "03"
            "unit_area" => 1151
            "unit_open_area" => 0
            "is_allotted" => "1"
            "is_occupied" => "1"
            "occupancy_type" => "reserved"
            "occupied_by" => "owner"
            "effective_date" => "2022-01-20T18:30:00.000000Z"
            "cancel_date" => "-000001-11-29T18:06:32.000000Z"
            "unit_total_water_inlets" => 0
          ]
          "invoice_number" => "INV-25_26-00012"
          "payment_status" => "unpaid"
          "bill_to" => "Sri  Mahesh Chaurasiya"
          "member_gstin" => ""
          "member_id" => 16
          "trial" => "live"
          "outstanding_principal" => 0
          "outstanding_interest" => 0
          "invoice_particulars" => array:5 [
            0 => array:17 [
              "soc_id" => "8485"
              "unit_invoice_id" => "bool"
              "invoice_number" => "INV-25_26-00012"
              "unit_id" => "3"
              "rule_id" => 15
              "particular" => "MaintenanceFee"
              "particular_name" => "Maintenance Fee"
              "amount" => 10500
              "bill_type" => "maintenance"
              "hsn_sac_code" => ""
              "is_particular_paid" => "N"
              "particular_paid_amount" => 0
              "booking_ids" => ""
              "apply_late_payment_interest" => 1
              "tax_applicable" => 0
              "tax_exemptions" => 0
              "fk_rule_id" => 15
            ]
            1 => array:17 [
              "soc_id" => "8485"
              "unit_invoice_id" => "bool"
              "invoice_number" => "INV-25_26-00012"
              "unit_id" => "3"
              "rule_id" => 25
              "particular" => "SinkingFund"
              "particular_name" => "Sinking Fund"
              "amount" => 393
              "bill_type" => "maintenance"
              "hsn_sac_code" => ""
              "is_particular_paid" => "N"
              "particular_paid_amount" => 0
              "booking_ids" => ""
              "apply_late_payment_interest" => 1
              "tax_applicable" => 0
              "tax_exemptions" => 0
              "fk_rule_id" => 25
            ]
            2 => array:15 [
              "soc_id" => "8485"
              "unit_invoice_id" => "bool"
              "invoice_number" => "INV-25_26-00012"
              "unit_id" => "3"
              "rule_id" => 22
              "particular" => "parking-2wheeler"
              "particular_name" => "Parking-2wheeler"
              "amount" => 300
              "hsn_sac_code" => ""
              "is_particular_paid" => "N"
              "particular_paid_amount" => 0
              "booking_ids" => ""
              "tax_applicable" => 0
              "tax_exemptions" => 0
              "fk_rule_id" => 22
            ]
            3 => array:17 [
              "soc_id" => "8485"
              "unit_invoice_id" => "bool"
              "invoice_number" => "INV-25_26-00012"
              "unit_id" => "3"
              "rule_id" => 26
              "particular" => "RepairFund"
              "particular_name" => " Repair Fund"
              "amount" => 1176
              "bill_type" => "maintenance"
              "hsn_sac_code" => ""
              "is_particular_paid" => "N"
              "particular_paid_amount" => 0
              "booking_ids" => ""
              "apply_late_payment_interest" => 1
              "tax_applicable" => 0
              "tax_exemptions" => 0
              "fk_rule_id" => 26
            ]
            4 => array:17 [
              "soc_id" => "8485"
              "unit_invoice_id" => "bool"
              "invoice_number" => "INV-25_26-00012"
              "unit_id" => "3"
              "rule_id" => 41
              "particular" => "EducationAndTrainingFund"
              "particular_name" => " Education And Training Fund"
              "amount" => 30
              "bill_type" => "maintenance"
              "hsn_sac_code" => ""
              "is_particular_paid" => "N"
              "particular_paid_amount" => 0
              "booking_ids" => ""
              "apply_late_payment_interest" => 1
              "tax_applicable" => 0
              "tax_exemptions" => 0
              "fk_rule_id" => 41
            ]
          ]
          "unit_invoice_number" => "INV-25_26-00012"
          "invoice_date" => "2025-07-01"
          "from_date" => "2025-07-01"
          "to_date" => "2025-09-30"
          "invoice_amount_detail" => array:4 [
            "totalInvoiceAmount" => 0
            "totalTaxApplicable" => 0
            "totalTaxExemption" => 0
            "finalInvoiceAmount" => 12399
          ]
        ]
      ]
      "arrMemberDetail" => array:17 [
        "unit_flat_number" => "03"
        "soc_building_name" => "Flat"
        "soc_building_floor" => 1
        "member_first_name" => "Sri "
        "member_last_name" => "Mahesh Chaurasiya"
        "member_mobile_number" => "919172361221"
        "member_email_id" => "<EMAIL>"
        "unit_area" => 1151
        "unit_open_area" => 0
        "unit_type" => "flat"
        "occupied_by" => "owner"
        "gstin" => ""
        "vpa" => null
        "in_queue" => 0
        "parking_number" => "P-03"
        "parking_type" => "open parking"
        "allotment_for" => "4wheeler"
      ]
      "arrLatePaymentStack" => array:6 [
        0 => array:26 [
          "id" => 1255
          "fk_rule_id" => 15
          "invoice_number" => "INV-25_26-00003"
          "particular" => "MaintenanceFee"
          "apply_late_payment_interest" => 1
          "amount" => 10500
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Maintenance Fee"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 10500
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2025-03-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 549.739726
        ]
        1 => array:26 [
          "id" => 1256
          "fk_rule_id" => 25
          "invoice_number" => "INV-25_26-00003"
          "particular" => "SinkingFund"
          "apply_late_payment_interest" => 1
          "amount" => 393
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Sinking Fund"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 393
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2025-03-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 20.575973
        ]
        2 => array:26 [
          "id" => 1257
          "fk_rule_id" => 26
          "invoice_number" => "INV-25_26-00003"
          "particular" => "RepairFund"
          "apply_late_payment_interest" => 1
          "amount" => 1176
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => null
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 1176
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2025-03-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 61.570849
        ]
        3 => array:26 [
          "id" => 1258
          "fk_rule_id" => 41
          "invoice_number" => "INV-25_26-00003"
          "particular" => "EducationAndTrainingFund"
          "apply_late_payment_interest" => 1
          "amount" => 30
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => null
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 30
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2025-03-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 1.570685
        ]
        4 => array:26 [
          "id" => 1259
          "fk_rule_id" => 7
          "invoice_number" => "INV-25_26-00003"
          "particular" => "Noc"
          "apply_late_payment_interest" => 1
          "amount" => 0
          "is_particular_paid" => "Y"
          "particular_paid_amount" => 0
          "display_name" => "Non-Occupancy Charges"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 0
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2025-03-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 0
        ]
        5 => array:26 [
          "id" => 1260
          "fk_rule_id" => 22
          "invoice_number" => "INV-25_26-00003"
          "particular" => "parking-2wheeler"
          "apply_late_payment_interest" => 1
          "amount" => 300
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Parking"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 300
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2025-03-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 15.706849
        ]
      ]
      "latePaymentamount" => 0
      "intrestPrinciple" => 12399
      "arrInvoiceType" => array:1 [
        "invoice_type" => array:1 [
          0 => "preview"
        ]
      ]
    ]
  ]
  "arrInvoiceGeneralSetting" => true
  "arrInvoiceSetting" => array:8 [
    "totalInvoiceCount" => 1
    "showInvoiceDetail" => 1
    "arrInvoiceGeneralNote" => array:9 [
      0 => "1. Pay your maintenance bill before due date to avoid late payment charges."
      1 => "2. Errors, If any, may please be brought to the notice of the Managing Committee by email within week."
      2 => "3. The due date of payment of Society Charges ( 1st  June for April - June quarter, 1st  September for July - September quarter, 1st December for October - December quarter and 1st  March for January - March quarter AND.  Billing date (1st April for April - June quarter, 1st July for July - September quarter, 1st October for October - December quarter and 1st January for January - March quarter) of payment of Society Charges for the financial year 2022-23 onwards and that the payment received after the due date shall attract an interest @ 21% per annum from the bill date to the actual date of payment. Members paying by cheque must ensure that payment is credited to the bank account of the Society by the due date and that the date of credit to the bank is taken into account and not the cheque deposit date."
      3 => "4. Use convenient payment options to pay your dues through online or cheque."
      4 => "5. To pay download oneapp mobile app or visit www.cubeoneapp.com"
      5 => ""
      6 => "6. Bank Details:- The Mumbai District Central Co-Op. Bank Ltd., A/c No.**************, IFSC Code: MDCB0680018, Branch: Anushaktinagar Branch."
      7 => ""
      8 => "This is a computer generated bill, hence a signature is not required."
    ]
    "showInterestBreakup" => 1
    "showChsoneFooter" => true
    "invoiceFontSize" => "11"
    "showBankDetail" => 1
    "arrBankDetail" => array:19 [
      "account_id" => 4
      "soc_id" => 8485
      "ledger_account_id" => 200
      "account_name" => "NATRAJ CHS LTD"
      "bank_name" => "STATE BANK OF INDIA"
      "branch" => "SECTOR-12 KHARGHAR"
      "account_number" => "***********"
      "bank_address" => "SHOP NO.20 AND 21, GROUND FLOOR AND 21 AND 22 ON 1ST FLOOR, RAHUL APT CHS"
      "bank_city" => "SECTOR-12, KHARGHAR, NAVI MUMBAI"
      "bank_ifsc" => "SBIN0017265"
      "default_account" => 1
      "created_by" => 1066
      "added_on" => "2024-07-22T22:38:59.000000Z"
      "modified_on" => "2024-07-22T22:38:59.000000Z"
      "status" => 1
      "group_id" => 5
      "active_for_payments" => 0
      "default_bank_for_incidental" => 1
      "default_bank_for_nonmember" => 1
    ]
  ]
  "arrSocietyDetail" => array:35 [
    "soc_id" => 8485
    "soc_type_id" => "residential"
    "soc_name" => "Shweta Co op Housing Society Ltd"
    "soc_reg_name" => ""
    "soc_subdomain_name" => "society"
    "soc_reg_num" => ""
    "soc_gst_number" => ""
    "place_of_supply" => null
    "soc_num_of_units" => 0
    "soc_plot_area" => 10
    "soc_address_1" => "1905, Plot No. 4 & 6, Sector 30a"
    "soc_address_2" => "Vashi, Navi Mumbai"
    "soc_landmark" => "Bhiwandi"
    "soc_city_or_town" => "Mumbai"
    "soc_state" => "Maharashtra"
    "soc_pincode" => 4546454
    "added_on" => null
    "modified_on" => "2025-06-25 19:42:36"
    "status" => 1
    "completed" => 1
    "firm_id" => 0
    "firm_name" => ""
    "firm_address" => ""
    "soc_units_added" => 0
    "soc_registration_date" => null
    "api_key" => "780971e3b4204c050ee1e77e2e1638e94c38169e"
    "api_server_key" => "e462cc42e4239eecb985e4529bc2fa1e4c1e2694"
    "soc_signature" => "signature."
    "salt" => "8a72f1ff7e39af2a35b74c7f384c323a2e7b0ba4"
    "prosim_comp_id" => null
    "is_auto_invoice_on" => 0
    "soc_office_email" => ""
    "soc_office_mobile" => ""
    "fsadmin_code" => null
    "soc_name_short" => "suraj"
  ]
  "arrInvoiceType" => array:1 [
    "invoice_type" => array:1 [
      0 => "Preview"
    ]
  ]
  "arrMemberDetail" => array:17 [
    "unit_flat_number" => "03"
    "soc_building_name" => "Flat"
    "soc_building_floor" => 1
    "member_first_name" => "Sri "
    "member_last_name" => "Mahesh Chaurasiya"
    "member_mobile_number" => "919172361221"
    "member_email_id" => "<EMAIL>"
    "unit_area" => 1151
    "unit_open_area" => 0
    "unit_type" => "flat"
    "occupied_by" => "owner"
    "gstin" => ""
    "vpa" => null
    "in_queue" => 0
    "parking_number" => "P-03"
    "parking_type" => "open parking"
    "allotment_for" => "4wheeler"
  ]
  "arrLastPeriodPaymentTransaction" => []
  "arrIncomeInvoiceDetail" => array:25 [
    "soc_id" => "8485"
    "unit_id" => "3"
    "interest_amount" => 649
    "principal_amount" => 12399
    "advance_amount" => 0
    "roundoff_amount" => 0
    "start_date" => "Jul 01, 2025"
    "end_date" => "Sep 30, 2025"
    "bill_date" => "Jul 01, 2025"
    "due_date" => "31-08-2025"
    "arrUnitData" => array:17 [
      "unit_id" => "3"
      "unit_category" => "1151"
      "ledger_account_id" => 105
      "fk_unit_category_id" => 3
      "soc_building_id" => 1
      "soc_building_name" => "Flat"
      "soc_building_floor" => 1
      "unit_flat_number" => "03"
      "unit_area" => 1151
      "unit_open_area" => 0
      "is_allotted" => "1"
      "is_occupied" => "1"
      "occupancy_type" => "reserved"
      "occupied_by" => "owner"
      "effective_date" => "2022-01-20T18:30:00.000000Z"
      "cancel_date" => "-000001-11-29T18:06:32.000000Z"
      "unit_total_water_inlets" => 0
    ]
    "invoice_number" => "INV-25_26-00012"
    "payment_status" => "unpaid"
    "bill_to" => "Sri  Mahesh Chaurasiya"
    "member_gstin" => ""
    "member_id" => 16
    "trial" => "live"
    "outstanding_principal" => 0
    "outstanding_interest" => 0
    "invoice_particulars" => array:5 [
      0 => array:17 [
        "soc_id" => "8485"
        "unit_invoice_id" => "bool"
        "invoice_number" => "INV-25_26-00012"
        "unit_id" => "3"
        "rule_id" => 15
        "particular" => "MaintenanceFee"
        "particular_name" => "Maintenance Fee"
        "amount" => 10500
        "bill_type" => "maintenance"
        "hsn_sac_code" => ""
        "is_particular_paid" => "N"
        "particular_paid_amount" => 0
        "booking_ids" => ""
        "apply_late_payment_interest" => 1
        "tax_applicable" => 0
        "tax_exemptions" => 0
        "fk_rule_id" => 15
      ]
      1 => array:17 [
        "soc_id" => "8485"
        "unit_invoice_id" => "bool"
        "invoice_number" => "INV-25_26-00012"
        "unit_id" => "3"
        "rule_id" => 25
        "particular" => "SinkingFund"
        "particular_name" => "Sinking Fund"
        "amount" => 393
        "bill_type" => "maintenance"
        "hsn_sac_code" => ""
        "is_particular_paid" => "N"
        "particular_paid_amount" => 0
        "booking_ids" => ""
        "apply_late_payment_interest" => 1
        "tax_applicable" => 0
        "tax_exemptions" => 0
        "fk_rule_id" => 25
      ]
      2 => array:15 [
        "soc_id" => "8485"
        "unit_invoice_id" => "bool"
        "invoice_number" => "INV-25_26-00012"
        "unit_id" => "3"
        "rule_id" => 22
        "particular" => "parking-2wheeler"
        "particular_name" => "Parking-2wheeler"
        "amount" => 300
        "hsn_sac_code" => ""
        "is_particular_paid" => "N"
        "particular_paid_amount" => 0
        "booking_ids" => ""
        "tax_applicable" => 0
        "tax_exemptions" => 0
        "fk_rule_id" => 22
      ]
      3 => array:17 [
        "soc_id" => "8485"
        "unit_invoice_id" => "bool"
        "invoice_number" => "INV-25_26-00012"
        "unit_id" => "3"
        "rule_id" => 26
        "particular" => "RepairFund"
        "particular_name" => " Repair Fund"
        "amount" => 1176
        "bill_type" => "maintenance"
        "hsn_sac_code" => ""
        "is_particular_paid" => "N"
        "particular_paid_amount" => 0
        "booking_ids" => ""
        "apply_late_payment_interest" => 1
        "tax_applicable" => 0
        "tax_exemptions" => 0
        "fk_rule_id" => 26
      ]
      4 => array:17 [
        "soc_id" => "8485"
        "unit_invoice_id" => "bool"
        "invoice_number" => "INV-25_26-00012"
        "unit_id" => "3"
        "rule_id" => 41
        "particular" => "EducationAndTrainingFund"
        "particular_name" => " Education And Training Fund"
        "amount" => 30
        "bill_type" => "maintenance"
        "hsn_sac_code" => ""
        "is_particular_paid" => "N"
        "particular_paid_amount" => 0
        "booking_ids" => ""
        "apply_late_payment_interest" => 1
        "tax_applicable" => 0
        "tax_exemptions" => 0
        "fk_rule_id" => 41
      ]
    ]
    "unit_invoice_number" => "INV-25_26-00012"
    "invoice_date" => "2025-07-01"
    "from_date" => "2025-07-01"
    "to_date" => "2025-09-30"
    "invoice_amount_detail" => array:4 [
      "totalInvoiceAmount" => 0
      "totalTaxApplicable" => 0
      "totalTaxExemption" => 0
      "finalInvoiceAmount" => 12399
    ]
  ]
  "invoice_particular" => array:6 [
    0 => array:17 [
      "soc_id" => "8485"
      "unit_invoice_id" => "bool"
      "invoice_number" => "INV-25_26-00012"
      "unit_id" => "3"
      "rule_id" => 15
      "particular" => "MaintenanceFee"
      "particular_name" => "Maintenance Fee"
      "amount" => 10500
      "bill_type" => "maintenance"
      "hsn_sac_code" => ""
      "is_particular_paid" => "N"
      "particular_paid_amount" => 0
      "booking_ids" => ""
      "apply_late_payment_interest" => 1
      "tax_applicable" => 0
      "tax_exemptions" => 0
      "fk_rule_id" => 15
    ]
    1 => array:17 [
      "soc_id" => "8485"
      "unit_invoice_id" => "bool"
      "invoice_number" => "INV-25_26-00012"
      "unit_id" => "3"
      "rule_id" => 25
      "particular" => "SinkingFund"
      "particular_name" => "Sinking Fund"
      "amount" => 393
      "bill_type" => "maintenance"
      "hsn_sac_code" => ""
      "is_particular_paid" => "N"
      "particular_paid_amount" => 0
      "booking_ids" => ""
      "apply_late_payment_interest" => 1
      "tax_applicable" => 0
      "tax_exemptions" => 0
      "fk_rule_id" => 25
    ]
    2 => array:15 [
      "soc_id" => "8485"
      "unit_invoice_id" => "bool"
      "invoice_number" => "INV-25_26-00012"
      "unit_id" => "3"
      "rule_id" => 22
      "particular" => "parking-2wheeler"
      "particular_name" => "Parking-2wheeler"
      "amount" => 300
      "hsn_sac_code" => ""
      "is_particular_paid" => "N"
      "particular_paid_amount" => 0
      "booking_ids" => ""
      "tax_applicable" => 0
      "tax_exemptions" => 0
      "fk_rule_id" => 22
    ]
    3 => array:17 [
      "soc_id" => "8485"
      "unit_invoice_id" => "bool"
      "invoice_number" => "INV-25_26-00012"
      "unit_id" => "3"
      "rule_id" => 26
      "particular" => "RepairFund"
      "particular_name" => " Repair Fund"
      "amount" => 1176
      "bill_type" => "maintenance"
      "hsn_sac_code" => ""
      "is_particular_paid" => "N"
      "particular_paid_amount" => 0
      "booking_ids" => ""
      "apply_late_payment_interest" => 1
      "tax_applicable" => 0
      "tax_exemptions" => 0
      "fk_rule_id" => 26
    ]
    4 => array:17 [
      "soc_id" => "8485"
      "unit_invoice_id" => "bool"
      "invoice_number" => "INV-25_26-00012"
      "unit_id" => "3"
      "rule_id" => 41
      "particular" => "EducationAndTrainingFund"
      "particular_name" => " Education And Training Fund"
      "amount" => 30
      "bill_type" => "maintenance"
      "hsn_sac_code" => ""
      "is_particular_paid" => "N"
      "particular_paid_amount" => 0
      "booking_ids" => ""
      "apply_late_payment_interest" => 1
      "tax_applicable" => 0
      "tax_exemptions" => 0
      "fk_rule_id" => 41
    ]
    5 => array:3 [
      "particular_name" => "Delayed Payment Charges (21% per year)<br> (Previous Due Amount: 12399)"
      "amount" => 649
      "hsn_sac_code" => ""
    ]
  ]
  "rate" => 21
  "intrestPrinciple" => 12399
  "interestAmount" => 649
  "invoiceTotalAmount" => 13048
  "advanceAmount" => 0
  "grandTotalAmount" => 13048
  "outstandingPrincipalAmount" => 0
  "outstandingInterestAmount" => 0
  "balanceDue" => 13048
  "rupeesInWord" => "Thirteen Thousand Forty Eight Rupees "
  "arrInvoiceGeneralNote" => array:9 [
    0 => "1. Pay your maintenance bill before due date to avoid late payment charges."
    1 => "2. Errors, If any, may please be brought to the notice of the Managing Committee by email within week."
    2 => "3. The due date of payment of Society Charges ( 1st  June for April - June quarter, 1st  September for July - September quarter, 1st December for October - December quarter and 1st  March for January - March quarter AND.  Billing date (1st April for April - June quarter, 1st July for July - September quarter, 1st October for October - December quarter and 1st January for January - March quarter) of payment of Society Charges for the financial year 2022-23 onwards and that the payment received after the due date shall attract an interest @ 21% per annum from the bill date to the actual date of payment. Members paying by cheque must ensure that payment is credited to the bank account of the Society by the due date and that the date of credit to the bank is taken into account and not the cheque deposit date."
    3 => "4. Use convenient payment options to pay your dues through online or cheque."
    4 => "5. To pay download oneapp mobile app or visit www.cubeoneapp.com"
    5 => ""
    6 => "6. Bank Details:- The Mumbai District Central Co-Op. Bank Ltd., A/c No.**************, IFSC Code: MDCB0680018, Branch: Anushaktinagar Branch."
    7 => ""
    8 => "This is a computer generated bill, hence a signature is not required."
  ]
  "arrBankDetail" => array:19 [
    "account_id" => 4
    "soc_id" => 8485
    "ledger_account_id" => 200
    "account_name" => "NATRAJ CHS LTD"
    "bank_name" => "STATE BANK OF INDIA"
    "branch" => "SECTOR-12 KHARGHAR"
    "account_number" => "***********"
    "bank_address" => "SHOP NO.20 AND 21, GROUND FLOOR AND 21 AND 22 ON 1ST FLOOR, RAHUL APT CHS"
    "bank_city" => "SECTOR-12, KHARGHAR, NAVI MUMBAI"
    "bank_ifsc" => "SBIN0017265"
    "default_account" => 1
    "created_by" => 1066
    "added_on" => "2024-07-22T22:38:59.000000Z"
    "modified_on" => "2024-07-22T22:38:59.000000Z"
    "status" => 1
    "group_id" => 5
    "active_for_payments" => 0
    "default_bank_for_incidental" => 1
    "default_bank_for_nonmember" => 1
  ]
]

Like for sing data with array 3 which is my unit_id but when multiple unit_id data is coming it should create same pdf with muiple pages.

data is coming for this scenario as follows

array:21 [ // app/Console/Commands/Workflows/Income/PreviewWithGenerateDownloadWorkflow.php:102
  "arrAllUnitInvoiceDetail" => array:2 [
    3 => array:31 [
      "error" => false
      "showBankDetail" => 0
      "invoiceFontSize" => 9
      "showBankWithoutTaxParitcular" => 2
      "invoiceTaxAmount" => 0
      "arrLateChargesDetail" => array:2 [
        0 => array:16 [
          "id" => 1
          "simple_interest" => 21
          "grace_period" => 62
          "grace_duration" => "62"
          "effective_date" => "2021-03-31T18:30:00.000000Z"
          "calculate_from" => "billdate"
          "calculate_for" => "perday"
          "created_date" => "2022-01-21T12:03:28.000000Z"
          "created_by" => 1066
          "updated_date" => "2025-01-13T06:50:06.000000Z"
          "updated_by" => 1066
          "soc_id" => 8485
          "interest_amount_type" => "percentage"
          "interest_type" => "simple_interest"
          "applicable_taxes" => ""
          "type" => "maintenance"
        ]
        "interest_text" => "% per year"
      ]
      "originalColumnCount" => 2
      "defaultColumnCount" => 2
      "arrTaxDetail" => ""
      "arrLateChargeTaxClass" => ""
      "arrAppliedTaxDetail" => ""
      "arrAppliedTaxCategory" => ""
      "arrTaxCategoryParticular" => ""
      "arrTaxCategoryParticularSubtotal" => ""
      "lateChargeTotalTax" => ""
      "arrAllAppliedTaxDetail" => []
      "invoiceTotalAmount" => 13048
      "grandTotalAmount" => 13048
      "outstandingPrincipalAmount" => 0
      "outstandingInterestAmount" => 0
      "interestAmount" => 649
      "advanceAmount" => 0
      "associateMembers" => ""
      "balanceDue" => 13048
      "rupeesInWord" => "Thirteen Thousand Forty Eight Rupees "
      "arrIncomeInvoiceDetail" => array:1 [
        0 => array:25 [
          "soc_id" => "8485"
          "unit_id" => "3"
          "interest_amount" => 649
          "principal_amount" => 12399
          "advance_amount" => 0
          "roundoff_amount" => 0
          "start_date" => "2025-07-01"
          "end_date" => "2025-09-30"
          "bill_date" => "2025-07-01"
          "due_date" => "31-08-2025"
          "arrUnitData" => array:17 [ …17]
          "invoice_number" => "INV-25_26-00012"
          "payment_status" => "unpaid"
          "bill_to" => "Sri  Mahesh Chaurasiya"
          "member_gstin" => ""
          "member_id" => 16
          "trial" => "live"
          "outstanding_principal" => 0
          "outstanding_interest" => 0
          "invoice_particulars" => array:5 [ …5]
          "unit_invoice_number" => "INV-25_26-00012"
          "invoice_date" => "2025-07-01"
          "from_date" => "2025-07-01"
          "to_date" => "2025-09-30"
          "invoice_amount_detail" => array:4 [ …4]
        ]
      ]
      "arrMemberDetail" => array:17 [
        "unit_flat_number" => "03"
        "soc_building_name" => "Flat"
        "soc_building_floor" => 1
        "member_first_name" => "Sri "
        "member_last_name" => "Mahesh Chaurasiya"
        "member_mobile_number" => "919172361221"
        "member_email_id" => "<EMAIL>"
        "unit_area" => 1151
        "unit_open_area" => 0
        "unit_type" => "flat"
        "occupied_by" => "owner"
        "gstin" => ""
        "vpa" => null
        "in_queue" => 0
        "parking_number" => "P-03"
        "parking_type" => "open parking"
        "allotment_for" => "4wheeler"
      ]
      "arrLatePaymentStack" => array:6 [
        0 => array:26 [
          "id" => 1255
          "fk_rule_id" => 15
          "invoice_number" => "INV-25_26-00003"
          "particular" => "MaintenanceFee"
          "apply_late_payment_interest" => 1
          "amount" => 10500
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Maintenance Fee"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 10500
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2025-03-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 549.739726
        ]
        1 => array:26 [
          "id" => 1256
          "fk_rule_id" => 25
          "invoice_number" => "INV-25_26-00003"
          "particular" => "SinkingFund"
          "apply_late_payment_interest" => 1
          "amount" => 393
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Sinking Fund"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 393
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2025-03-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 20.575973
        ]
        2 => array:26 [
          "id" => 1257
          "fk_rule_id" => 26
          "invoice_number" => "INV-25_26-00003"
          "particular" => "RepairFund"
          "apply_late_payment_interest" => 1
          "amount" => 1176
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => null
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 1176
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2025-03-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 61.570849
        ]
        3 => array:26 [
          "id" => 1258
          "fk_rule_id" => 41
          "invoice_number" => "INV-25_26-00003"
          "particular" => "EducationAndTrainingFund"
          "apply_late_payment_interest" => 1
          "amount" => 30
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => null
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 30
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2025-03-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 1.570685
        ]
        4 => array:26 [
          "id" => 1259
          "fk_rule_id" => 7
          "invoice_number" => "INV-25_26-00003"
          "particular" => "Noc"
          "apply_late_payment_interest" => 1
          "amount" => 0
          "is_particular_paid" => "Y"
          "particular_paid_amount" => 0
          "display_name" => "Non-Occupancy Charges"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 0
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2025-03-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 0
        ]
        5 => array:26 [
          "id" => 1260
          "fk_rule_id" => 22
          "invoice_number" => "INV-25_26-00003"
          "particular" => "parking-2wheeler"
          "apply_late_payment_interest" => 1
          "amount" => 300
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Parking"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 300
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2025-03-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 15.706849
        ]
      ]
      "latePaymentamount" => 0
      "intrestPrinciple" => 12399
      "arrInvoiceType" => array:1 [
        "invoice_type" => array:1 [
          0 => "preview"
        ]
      ]
    ]
    4 => array:31 [
      "error" => false
      "showBankDetail" => 0
      "invoiceFontSize" => 9
      "showBankWithoutTaxParitcular" => 2
      "invoiceTaxAmount" => 0
      "arrLateChargesDetail" => array:2 [
        0 => array:16 [
          "id" => 1
          "simple_interest" => 21
          "grace_period" => 62
          "grace_duration" => "62"
          "effective_date" => "2021-03-31T18:30:00.000000Z"
          "calculate_from" => "billdate"
          "calculate_for" => "perday"
          "created_date" => "2022-01-21T12:03:28.000000Z"
          "created_by" => 1066
          "updated_date" => "2025-01-13T06:50:06.000000Z"
          "updated_by" => 1066
          "soc_id" => 8485
          "interest_amount_type" => "percentage"
          "interest_type" => "simple_interest"
          "applicable_taxes" => ""
          "type" => "maintenance"
        ]
        "interest_text" => "% per year"
      ]
      "originalColumnCount" => 2
      "defaultColumnCount" => 2
      "arrTaxDetail" => ""
      "arrLateChargeTaxClass" => ""
      "arrAppliedTaxDetail" => ""
      "arrAppliedTaxCategory" => ""
      "arrTaxCategoryParticular" => ""
      "arrTaxCategoryParticularSubtotal" => ""
      "lateChargeTotalTax" => ""
      "arrAllAppliedTaxDetail" => []
      "invoiceTotalAmount" => 23093
      "grandTotalAmount" => 23093
      "outstandingPrincipalAmount" => 133200
      "outstandingInterestAmount" => 44412
      "interestAmount" => 10541
      "advanceAmount" => 0
      "associateMembers" => ""
      "balanceDue" => 200705
      "rupeesInWord" => "Two Lakh Seven Hundred Five Rupees "
      "arrIncomeInvoiceDetail" => array:1 [
        0 => array:25 [
          "soc_id" => "8485"
          "unit_id" => "4"
          "interest_amount" => 10541
          "principal_amount" => 201332
          "advance_amount" => 0
          "roundoff_amount" => 0
          "start_date" => "2025-07-01"
          "end_date" => "2025-09-30"
          "bill_date" => "2025-07-01"
          "due_date" => "31-08-2025"
          "arrUnitData" => array:17 [ …17]
          "invoice_number" => "INV-25_26-00012"
          "payment_status" => "unpaid"
          "bill_to" => "Miss Shweta Danawale"
          "member_gstin" => ""
          "member_id" => 4
          "trial" => "live"
          "outstanding_principal" => 133200
          "outstanding_interest" => 44412
          "invoice_particulars" => array:4 [ …4]
          "unit_invoice_number" => "INV-25_26-00012"
          "invoice_date" => "2025-07-01"
          "from_date" => "2025-07-01"
          "to_date" => "2025-09-30"
          "invoice_amount_detail" => array:4 [ …4]
        ]
      ]
      "arrMemberDetail" => array:17 [
        "unit_flat_number" => "04"
        "soc_building_name" => "Flat"
        "soc_building_floor" => 2
        "member_first_name" => "Miss"
        "member_last_name" => "Shweta Danawale"
        "member_mobile_number" => "917208444884"
        "member_email_id" => "<EMAIL>"
        "unit_area" => 1483
        "unit_open_area" => 0
        "unit_type" => "flat"
        "occupied_by" => "owner"
        "gstin" => ""
        "vpa" => null
        "in_queue" => 0
        "parking_number" => "P-04"
        "parking_type" => "open parking"
        "allotment_for" => "4wheeler"
      ]
      "arrLatePaymentStack" => array:84 [
        0 => array:19 [
          "id" => 0
          "fk_rule_id" => 0
          "invoice_number" => "M-INV00004"
          "particular" => "Principal Arrears of Jun"
          "apply_late_payment_interest" => 1
          "amount" => 133200
          "is_particular_paid" => "N"
          "particular_paid_amount" => 42595
          "display_name" => ""
          "tax_applicable" => 0
          "tax_exemptions" => 0
          "invoice_date" => "2021-06-07T18:30:00.000000Z"
          "unpaid_amount" => 90605
          "delay_days" => null
          "rate" => 21
          "type" => "simple_interest"
          "amount_type" => "percentage"
          "payment_date" => "2025-06-30"
          "payment_start_date" => "2021-03-31T18:30:00.000000Z"
        ]
        1 => array:16 [
          "id" => 204
          "fk_rule_id" => 16
          "invoice_number" => "M-INV00004"
          "particular" => "MaintenanceFee"
          "apply_late_payment_interest" => 1
          "amount" => 10500
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Maintenance Fee"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 10500
          "type" => "maintenance"
          "payment_date" => "2025-06-30"
          "payment_start_date" => "2021-03-31T18:30:00.000000Z"
        ]
        2 => array:16 [
          "id" => 205
          "fk_rule_id" => 27
          "invoice_number" => "M-INV00004"
          "particular" => "SinkingFund"
          "apply_late_payment_interest" => 1
          "amount" => 507
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Sinking Fund"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 507
          "type" => "maintenance"
          "payment_date" => "2025-06-30"
          "payment_start_date" => "2021-03-31T18:30:00.000000Z"
        ]
        3 => array:16 [
          "id" => 206
          "fk_rule_id" => 28
          "invoice_number" => "M-INV00004"
          "particular" => "RepairFund"
          "apply_late_payment_interest" => 1
          "amount" => 1515
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => null
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 1515
          "type" => "maintenance"
          "payment_date" => "2025-06-30"
          "payment_start_date" => "2021-03-31T18:30:00.000000Z"
        ]
        4 => array:16 [
          "id" => 207
          "fk_rule_id" => 8
          "invoice_number" => "M-INV00004"
          "particular" => "Noc"
          "apply_late_payment_interest" => 1
          "amount" => 0
          "is_particular_paid" => "Y"
          "particular_paid_amount" => 0
          "display_name" => "Non-Occupancy Charges"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 0
          "type" => "maintenance"
          "payment_date" => "2025-06-30"
          "payment_start_date" => "2021-03-31T18:30:00.000000Z"
        ]
        5 => array:16 [
          "id" => 208
          "fk_rule_id" => 4
          "invoice_number" => "M-INV00004"
          "particular" => "Parking-4wheeler"
          "apply_late_payment_interest" => 1
          "amount" => 45000
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Sinking Fund"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => "2021-04-01"
          "unpaid_amount" => 45000
          "type" => "incident"
          "payment_date" => "2025-06-30"
          "payment_start_date" => "2021-03-31T18:30:00.000000Z"
        ]
        6 => array:26 [
          "id" => 260
          "fk_rule_id" => 16
          "invoice_number" => "M-INV00015"
          "particular" => "MaintenanceFee"
          "apply_late_payment_interest" => 1
          "amount" => 10500
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Maintenance Fee"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 10500
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2021-08-12T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 549.739726
        ]
        7 => array:26 [
          "id" => 261
          "fk_rule_id" => 27
          "invoice_number" => "M-INV00015"
          "particular" => "SinkingFund"
          "apply_late_payment_interest" => 1
          "amount" => 507
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Sinking Fund"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 507
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2021-08-12T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 26.544575
        ]
        8 => array:26 [
          "id" => 262
          "fk_rule_id" => 28
          "invoice_number" => "M-INV00015"
          "particular" => "RepairFund"
          "apply_late_payment_interest" => 1
          "amount" => 1515
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => null
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 1515
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2021-08-12T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 79.319589
        ]
        9 => array:26 [
          "id" => 263
          "fk_rule_id" => 8
          "invoice_number" => "M-INV00015"
          "particular" => "Noc"
          "apply_late_payment_interest" => 1
          "amount" => 0
          "is_particular_paid" => "Y"
          "particular_paid_amount" => 0
          "display_name" => "Non-Occupancy Charges"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 0
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2021-08-12T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 0
        ]
        10 => array:26 [
          "id" => 322
          "fk_rule_id" => 16
          "invoice_number" => "M-INV00027"
          "particular" => "MaintenanceFee"
          "apply_late_payment_interest" => 1
          "amount" => 10500
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Maintenance Fee"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 10500
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2021-12-25T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 549.739726
        ]
        11 => array:26 [
          "id" => 323
          "fk_rule_id" => 27
          "invoice_number" => "M-INV00027"
          "particular" => "SinkingFund"
          "apply_late_payment_interest" => 1
          "amount" => 507
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Sinking Fund"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 507
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2021-12-25T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 26.544575
        ]
        12 => array:26 [
          "id" => 324
          "fk_rule_id" => 28
          "invoice_number" => "M-INV00027"
          "particular" => "RepairFund"
          "apply_late_payment_interest" => 1
          "amount" => 1515
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => null
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 1515
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2021-12-25T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 79.319589
        ]
        13 => array:26 [
          "id" => 325
          "fk_rule_id" => 8
          "invoice_number" => "M-INV00027"
          "particular" => "Noc"
          "apply_late_payment_interest" => 1
          "amount" => 0
          "is_particular_paid" => "Y"
          "particular_paid_amount" => 0
          "display_name" => "Non-Occupancy Charges"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 0
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2021-12-25T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 0
        ]
        14 => array:26 [
          "id" => 403
          "fk_rule_id" => 16
          "invoice_number" => "M-INV00043"
          "particular" => "MaintenanceFee"
          "apply_late_payment_interest" => 1
          "amount" => 10500
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Maintenance Fee"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 10500
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2022-03-05T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 549.739726
        ]
        15 => array:26 [
          "id" => 404
          "fk_rule_id" => 27
          "invoice_number" => "M-INV00043"
          "particular" => "SinkingFund"
          "apply_late_payment_interest" => 1
          "amount" => 507
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Sinking Fund"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 507
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2022-03-05T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 26.544575
        ]
        16 => array:26 [
          "id" => 405
          "fk_rule_id" => 28
          "invoice_number" => "M-INV00043"
          "particular" => "RepairFund"
          "apply_late_payment_interest" => 1
          "amount" => 1515
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => null
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 1515
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2022-03-05T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 79.319589
        ]
        17 => array:26 [
          "id" => 406
          "fk_rule_id" => 8
          "invoice_number" => "M-INV00043"
          "particular" => "Noc"
          "apply_late_payment_interest" => 1
          "amount" => 0
          "is_particular_paid" => "Y"
          "particular_paid_amount" => 0
          "display_name" => "Non-Occupancy Charges"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 0
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2022-03-05T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 0
        ]
        18 => array:26 [
          "id" => 431
          "fk_rule_id" => 16
          "invoice_number" => "M-INV00048"
          "particular" => "MaintenanceFee"
          "apply_late_payment_interest" => 1
          "amount" => 10500
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Maintenance Fee"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 10500
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2022-03-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 549.739726
        ]
        19 => array:26 [
          "id" => 432
          "fk_rule_id" => 27
          "invoice_number" => "M-INV00048"
          "particular" => "SinkingFund"
          "apply_late_payment_interest" => 1
          "amount" => 507
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Sinking Fund"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 507
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2022-03-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 26.544575
        ]
        20 => array:26 [
          "id" => 433
          "fk_rule_id" => 28
          "invoice_number" => "M-INV00048"
          "particular" => "RepairFund"
          "apply_late_payment_interest" => 1
          "amount" => 1515
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => null
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 1515
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2022-03-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 79.319589
        ]
        21 => array:26 [
          "id" => 434
          "fk_rule_id" => 42
          "invoice_number" => "M-INV00048"
          "particular" => "EducationAndTrainingFund"
          "apply_late_payment_interest" => 1
          "amount" => 30
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => null
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 30
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2022-03-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 1.570685
        ]
        22 => array:26 [
          "id" => 435
          "fk_rule_id" => 8
          "invoice_number" => "M-INV00048"
          "particular" => "Noc"
          "apply_late_payment_interest" => 1
          "amount" => 0
          "is_particular_paid" => "Y"
          "particular_paid_amount" => 0
          "display_name" => "Non-Occupancy Charges"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 0
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2022-03-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 0
        ]
        23 => array:26 [
          "id" => 498
          "fk_rule_id" => 16
          "invoice_number" => "M-INV00059"
          "particular" => "MaintenanceFee"
          "apply_late_payment_interest" => 1
          "amount" => 10500
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Maintenance Fee"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 10500
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2022-06-30T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 549.739726
        ]
        24 => array:26 [
          "id" => 499
          "fk_rule_id" => 27
          "invoice_number" => "M-INV00059"
          "particular" => "SinkingFund"
          "apply_late_payment_interest" => 1
          "amount" => 507
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Sinking Fund"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 507
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2022-06-30T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 26.544575
        ]
        25 => array:26 [
          "id" => 500
          "fk_rule_id" => 28
          "invoice_number" => "M-INV00059"
          "particular" => "RepairFund"
          "apply_late_payment_interest" => 1
          "amount" => 1515
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => null
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 1515
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2022-06-30T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 79.319589
        ]
        26 => array:26 [
          "id" => 501
          "fk_rule_id" => 42
          "invoice_number" => "M-INV00059"
          "particular" => "EducationAndTrainingFund"
          "apply_late_payment_interest" => 1
          "amount" => 30
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => null
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 30
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2022-06-30T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 1.570685
        ]
        27 => array:26 [
          "id" => 502
          "fk_rule_id" => 8
          "invoice_number" => "M-INV00059"
          "particular" => "Noc"
          "apply_late_payment_interest" => 1
          "amount" => 0
          "is_particular_paid" => "Y"
          "particular_paid_amount" => 0
          "display_name" => "Non-Occupancy Charges"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 0
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2022-06-30T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 0
        ]
        28 => array:26 [
          "id" => 565
          "fk_rule_id" => 16
          "invoice_number" => "M-INV00070"
          "particular" => "MaintenanceFee"
          "apply_late_payment_interest" => 1
          "amount" => 10500
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Maintenance Fee"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 10500
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2022-09-30T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 549.739726
        ]
        29 => array:26 [
          "id" => 566
          "fk_rule_id" => 27
          "invoice_number" => "M-INV00070"
          "particular" => "SinkingFund"
          "apply_late_payment_interest" => 1
          "amount" => 507
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Sinking Fund"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 507
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2022-09-30T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 26.544575
        ]
        30 => array:26 [
          "id" => 567
          "fk_rule_id" => 28
          "invoice_number" => "M-INV00070"
          "particular" => "RepairFund"
          "apply_late_payment_interest" => 1
          "amount" => 1515
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => null
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 1515
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2022-09-30T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 79.319589
        ]
        31 => array:26 [
          "id" => 568
          "fk_rule_id" => 42
          "invoice_number" => "M-INV00070"
          "particular" => "EducationAndTrainingFund"
          "apply_late_payment_interest" => 1
          "amount" => 30
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => null
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 30
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2022-09-30T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 1.570685
        ]
        32 => array:26 [
          "id" => 569
          "fk_rule_id" => 8
          "invoice_number" => "M-INV00070"
          "particular" => "Noc"
          "apply_late_payment_interest" => 1
          "amount" => 0
          "is_particular_paid" => "Y"
          "particular_paid_amount" => 0
          "display_name" => "Non-Occupancy Charges"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 0
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2022-09-30T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 0
        ]
        33 => array:26 [
          "id" => 631
          "fk_rule_id" => 16
          "invoice_number" => "M-INV00081"
          "particular" => "MaintenanceFee"
          "apply_late_payment_interest" => 1
          "amount" => 10500
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Maintenance Fee"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 10500
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2022-12-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 549.739726
        ]
        34 => array:26 [
          "id" => 632
          "fk_rule_id" => 27
          "invoice_number" => "M-INV00081"
          "particular" => "SinkingFund"
          "apply_late_payment_interest" => 1
          "amount" => 507
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Sinking Fund"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 507
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2022-12-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 26.544575
        ]
        35 => array:26 [
          "id" => 633
          "fk_rule_id" => 28
          "invoice_number" => "M-INV00081"
          "particular" => "RepairFund"
          "apply_late_payment_interest" => 1
          "amount" => 1515
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => null
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 1515
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2022-12-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 79.319589
        ]
        36 => array:26 [
          "id" => 634
          "fk_rule_id" => 42
          "invoice_number" => "M-INV00081"
          "particular" => "EducationAndTrainingFund"
          "apply_late_payment_interest" => 1
          "amount" => 30
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => null
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 30
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2022-12-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 1.570685
        ]
        37 => array:26 [
          "id" => 635
          "fk_rule_id" => 8
          "invoice_number" => "M-INV00081"
          "particular" => "Noc"
          "apply_late_payment_interest" => 1
          "amount" => 0
          "is_particular_paid" => "Y"
          "particular_paid_amount" => 0
          "display_name" => "Non-Occupancy Charges"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 0
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2022-12-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 0
        ]
        38 => array:26 [
          "id" => 701
          "fk_rule_id" => 16
          "invoice_number" => "M-INV00092"
          "particular" => "MaintenanceFee"
          "apply_late_payment_interest" => 1
          "amount" => 10500
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Maintenance Fee"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 10500
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2023-03-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 549.739726
        ]
        39 => array:26 [
          "id" => 702
          "fk_rule_id" => 27
          "invoice_number" => "M-INV00092"
          "particular" => "SinkingFund"
          "apply_late_payment_interest" => 1
          "amount" => 507
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Sinking Fund"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 507
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2023-03-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 26.544575
        ]
        40 => array:26 [
          "id" => 703
          "fk_rule_id" => 28
          "invoice_number" => "M-INV00092"
          "particular" => "RepairFund"
          "apply_late_payment_interest" => 1
          "amount" => 1515
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => null
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 1515
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2023-03-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 79.319589
        ]
        41 => array:26 [
          "id" => 704
          "fk_rule_id" => 42
          "invoice_number" => "M-INV00092"
          "particular" => "EducationAndTrainingFund"
          "apply_late_payment_interest" => 1
          "amount" => 30
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => null
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 30
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2023-03-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 1.570685
        ]
        42 => array:26 [
          "id" => 705
          "fk_rule_id" => 8
          "invoice_number" => "M-INV00092"
          "particular" => "Noc"
          "apply_late_payment_interest" => 1
          "amount" => 0
          "is_particular_paid" => "Y"
          "particular_paid_amount" => 0
          "display_name" => "Non-Occupancy Charges"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 0
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2023-03-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 0
        ]
        43 => array:26 [
          "id" => 772
          "fk_rule_id" => 16
          "invoice_number" => "M-INV00104"
          "particular" => "MaintenanceFee"
          "apply_late_payment_interest" => 1
          "amount" => 10500
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Maintenance Fee"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 10500
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2023-06-30T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 549.739726
        ]
        44 => array:26 [
          "id" => 773
          "fk_rule_id" => 27
          "invoice_number" => "M-INV00104"
          "particular" => "SinkingFund"
          "apply_late_payment_interest" => 1
          "amount" => 507
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Sinking Fund"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 507
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2023-06-30T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 26.544575
        ]
        45 => array:26 [
          "id" => 774
          "fk_rule_id" => 28
          "invoice_number" => "M-INV00104"
          "particular" => "RepairFund"
          "apply_late_payment_interest" => 1
          "amount" => 1515
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => null
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 1515
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2023-06-30T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 79.319589
        ]
        46 => array:26 [
          "id" => 775
          "fk_rule_id" => 42
          "invoice_number" => "M-INV00104"
          "particular" => "EducationAndTrainingFund"
          "apply_late_payment_interest" => 1
          "amount" => 30
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => null
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 30
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2023-06-30T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 1.570685
        ]
        47 => array:26 [
          "id" => 776
          "fk_rule_id" => 8
          "invoice_number" => "M-INV00104"
          "particular" => "Noc"
          "apply_late_payment_interest" => 1
          "amount" => 0
          "is_particular_paid" => "Y"
          "particular_paid_amount" => 0
          "display_name" => "Non-Occupancy Charges"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 0
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2023-06-30T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 0
        ]
        48 => array:26 [
          "id" => 838
          "fk_rule_id" => 16
          "invoice_number" => "M-INV00115"
          "particular" => "MaintenanceFee"
          "apply_late_payment_interest" => 1
          "amount" => 10500
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Maintenance Fee"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 10500
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2023-09-30T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 549.739726
        ]
        49 => array:26 [
          "id" => 839
          "fk_rule_id" => 27
          "invoice_number" => "M-INV00115"
          "particular" => "SinkingFund"
          "apply_late_payment_interest" => 1
          "amount" => 507
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Sinking Fund"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 507
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2023-09-30T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 26.544575
        ]
        50 => array:26 [
          "id" => 840
          "fk_rule_id" => 28
          "invoice_number" => "M-INV00115"
          "particular" => "RepairFund"
          "apply_late_payment_interest" => 1
          "amount" => 1515
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => null
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 1515
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2023-09-30T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 79.319589
        ]
        51 => array:26 [
          "id" => 841
          "fk_rule_id" => 42
          "invoice_number" => "M-INV00115"
          "particular" => "EducationAndTrainingFund"
          "apply_late_payment_interest" => 1
          "amount" => 30
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => null
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 30
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2023-09-30T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 1.570685
        ]
        52 => array:26 [
          "id" => 842
          "fk_rule_id" => 8
          "invoice_number" => "M-INV00115"
          "particular" => "Noc"
          "apply_late_payment_interest" => 1
          "amount" => 0
          "is_particular_paid" => "Y"
          "particular_paid_amount" => 0
          "display_name" => "Non-Occupancy Charges"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 0
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2023-09-30T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 0
        ]
        53 => array:26 [
          "id" => 843
          "fk_rule_id" => 10
          "invoice_number" => "M-INV00115"
          "particular" => "ChequeReturnCharges"
          "apply_late_payment_interest" => 1
          "amount" => 590
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Non-Occupancy Charges"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => "2023-10-01"
          "unpaid_amount" => 590
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2023-09-30T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 30.890137
        ]
        54 => array:26 [
          "id" => 904
          "fk_rule_id" => 16
          "invoice_number" => "M-INV00126"
          "particular" => "MaintenanceFee"
          "apply_late_payment_interest" => 1
          "amount" => 10500
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Maintenance Fee"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 10500
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2023-12-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 549.739726
        ]
        55 => array:26 [
          "id" => 905
          "fk_rule_id" => 27
          "invoice_number" => "M-INV00126"
          "particular" => "SinkingFund"
          "apply_late_payment_interest" => 1
          "amount" => 507
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Sinking Fund"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 507
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2023-12-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 26.544575
        ]
        56 => array:26 [
          "id" => 906
          "fk_rule_id" => 28
          "invoice_number" => "M-INV00126"
          "particular" => "RepairFund"
          "apply_late_payment_interest" => 1
          "amount" => 1515
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => null
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 1515
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2023-12-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 79.319589
        ]
        57 => array:26 [
          "id" => 907
          "fk_rule_id" => 42
          "invoice_number" => "M-INV00126"
          "particular" => "EducationAndTrainingFund"
          "apply_late_payment_interest" => 1
          "amount" => 30
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => null
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 30
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2023-12-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 1.570685
        ]
        58 => array:26 [
          "id" => 908
          "fk_rule_id" => 8
          "invoice_number" => "M-INV00126"
          "particular" => "Noc"
          "apply_late_payment_interest" => 1
          "amount" => 0
          "is_particular_paid" => "Y"
          "particular_paid_amount" => 0
          "display_name" => "Non-Occupancy Charges"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 0
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2023-12-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 0
        ]
        59 => array:26 [
          "id" => 980
          "fk_rule_id" => 16
          "invoice_number" => "M-INV-00004"
          "particular" => "MaintenanceFee"
          "apply_late_payment_interest" => 1
          "amount" => 10500
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Maintenance Fee"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 10500
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2024-03-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 549.739726
        ]
        60 => array:26 [
          "id" => 981
          "fk_rule_id" => 27
          "invoice_number" => "M-INV-00004"
          "particular" => "SinkingFund"
          "apply_late_payment_interest" => 1
          "amount" => 507
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Sinking Fund"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 507
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2024-03-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 26.544575
        ]
        61 => array:26 [
          "id" => 982
          "fk_rule_id" => 28
          "invoice_number" => "M-INV-00004"
          "particular" => "RepairFund"
          "apply_late_payment_interest" => 1
          "amount" => 1515
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => null
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 1515
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2024-03-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 79.319589
        ]
        62 => array:26 [
          "id" => 983
          "fk_rule_id" => 42
          "invoice_number" => "M-INV-00004"
          "particular" => "EducationAndTrainingFund"
          "apply_late_payment_interest" => 1
          "amount" => 30
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => null
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 30
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2024-03-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 1.570685
        ]
        63 => array:26 [
          "id" => 984
          "fk_rule_id" => 8
          "invoice_number" => "M-INV-00004"
          "particular" => "Noc"
          "apply_late_payment_interest" => 1
          "amount" => 0
          "is_particular_paid" => "Y"
          "particular_paid_amount" => 0
          "display_name" => "Non-Occupancy Charges"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 0
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2024-03-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 0
        ]
        64 => array:26 [
          "id" => 1063
          "fk_rule_id" => 16
          "invoice_number" => "M-INV-00015"
          "particular" => "MaintenanceFee"
          "apply_late_payment_interest" => 1
          "amount" => 10500
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Maintenance Fee"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 10500
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2024-06-30T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 549.739726
        ]
        65 => array:26 [
          "id" => 1064
          "fk_rule_id" => 27
          "invoice_number" => "M-INV-00015"
          "particular" => "SinkingFund"
          "apply_late_payment_interest" => 1
          "amount" => 507
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Sinking Fund"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 507
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2024-06-30T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 26.544575
        ]
        66 => array:26 [
          "id" => 1065
          "fk_rule_id" => 28
          "invoice_number" => "M-INV-00015"
          "particular" => "RepairFund"
          "apply_late_payment_interest" => 1
          "amount" => 1515
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => null
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 1515
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2024-06-30T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 79.319589
        ]
        67 => array:26 [
          "id" => 1066
          "fk_rule_id" => 42
          "invoice_number" => "M-INV-00015"
          "particular" => "EducationAndTrainingFund"
          "apply_late_payment_interest" => 1
          "amount" => 30
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => null
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 30
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2024-06-30T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 1.570685
        ]
        68 => array:26 [
          "id" => 1067
          "fk_rule_id" => 8
          "invoice_number" => "M-INV-00015"
          "particular" => "Noc"
          "apply_late_payment_interest" => 1
          "amount" => 0
          "is_particular_paid" => "Y"
          "particular_paid_amount" => 0
          "display_name" => "Non-Occupancy Charges"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 0
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2024-06-30T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 0
        ]
        69 => array:26 [
          "id" => 1129
          "fk_rule_id" => 16
          "invoice_number" => "M-INV-00026"
          "particular" => "MaintenanceFee"
          "apply_late_payment_interest" => 1
          "amount" => 10500
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Maintenance Fee"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 10500
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2024-09-30T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 549.739726
        ]
        70 => array:26 [
          "id" => 1130
          "fk_rule_id" => 27
          "invoice_number" => "M-INV-00026"
          "particular" => "SinkingFund"
          "apply_late_payment_interest" => 1
          "amount" => 507
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Sinking Fund"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 507
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2024-09-30T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 26.544575
        ]
        71 => array:26 [
          "id" => 1131
          "fk_rule_id" => 28
          "invoice_number" => "M-INV-00026"
          "particular" => "RepairFund"
          "apply_late_payment_interest" => 1
          "amount" => 1515
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => null
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 1515
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2024-09-30T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 79.319589
        ]
        72 => array:26 [
          "id" => 1132
          "fk_rule_id" => 42
          "invoice_number" => "M-INV-00026"
          "particular" => "EducationAndTrainingFund"
          "apply_late_payment_interest" => 1
          "amount" => 30
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => null
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 30
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2024-09-30T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 1.570685
        ]
        73 => array:26 [
          "id" => 1133
          "fk_rule_id" => 8
          "invoice_number" => "M-INV-00026"
          "particular" => "Noc"
          "apply_late_payment_interest" => 1
          "amount" => 0
          "is_particular_paid" => "Y"
          "particular_paid_amount" => 0
          "display_name" => "Non-Occupancy Charges"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 0
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2024-09-30T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 0
        ]
        74 => array:26 [
          "id" => 1195
          "fk_rule_id" => 16
          "invoice_number" => "M-INV-00037"
          "particular" => "MaintenanceFee"
          "apply_late_payment_interest" => 1
          "amount" => 10500
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Maintenance Fee"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 10500
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2024-12-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 549.739726
        ]
        75 => array:26 [
          "id" => 1196
          "fk_rule_id" => 27
          "invoice_number" => "M-INV-00037"
          "particular" => "SinkingFund"
          "apply_late_payment_interest" => 1
          "amount" => 507
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => "Sinking Fund"
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 507
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2024-12-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 26.544575
        ]
        76 => array:26 [
          "id" => 1197
          "fk_rule_id" => 28
          "invoice_number" => "M-INV-00037"
          "particular" => "RepairFund"
          "apply_late_payment_interest" => 1
          "amount" => 1515
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => null
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 1515
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
          "invoice_date" => "2024-12-31T18:30:00.000000Z"
          "start_date_id" => "last_invoice_create_date"
          "end_date" => "2025-06-30"
          "end_date_id" => "last_invoice_end_date"
          "delay_days" => "91"
          "rate" => 21
          "amount_type" => "percentage"
          "calculation_from" => "per day"
          "calculate_for" => "clearance"
          "calculate_from" => "billdate"
          "interest" => 79.319589
        ]
        77 => array:26 [
          "id" => 1198
          "fk_rule_id" => 42
          "invoice_number" => "M-INV-00037"
          "particular" => "EducationAndTrainingFund"
          "apply_late_payment_interest" => 1
          "amount" => 30
          "is_particular_paid" => "N"
          "particular_paid_amount" => 0
          "display_name" => null
          "tax_applicable" => "0"
          "tax_exemptions" => "0"
          "due_date" => ""
          "unpaid_amount" => 30
          "type" => "simple_interest"
          "start_date" => "2025-03-31"
           …11
        ]
        78 => array:26 [ …26]
        79 => array:26 [ …26]
        80 => array:26 [ …26]
        81 => array:26 [ …26]
        82 => array:26 [ …26]
        83 => array:26 [ …26]
      ]
      "latePaymentamount" => 0
      "intrestPrinciple" => 201332
      "arrInvoiceType" => array:1 [
        "invoice_type" => array:1 [ …1]
      ]
    ]
  ]
  "arrInvoiceGeneralSetting" => true
  "arrInvoiceSetting" => array:8 [
    "totalInvoiceCount" => 2
    "showInvoiceDetail" => 1
    "arrInvoiceGeneralNote" => array:9 [
      0 => "1. Pay your maintenance bill before due date to avoid late payment charges."
      1 => "2. Errors, If any, may please be brought to the notice of the Managing Committee by email within week."
      2 => "3. The due date of payment of Society Charges ( 1st  June for April - June quarter, 1st  September for July - September quarter, 1st December for October - December quarter and 1st  March for January - March quarter AND.  Billing date (1st April for April - June quarter, 1st July for July - September quarter, 1st October for October - December quarter and 1st January for January - March quarter) of payment of Society Charges for the financial year 2022-23 onwards and that the payment received after the due date shall attract an interest @ 21% per annum from the bill date to the actual date of payment. Members paying by cheque must ensure that payment is credited to the bank account of the Society by the due date and that the date of credit to the bank is taken into account and not the cheque deposit date."
      3 => "4. Use convenient payment options to pay your dues through online or cheque."
      4 => "5. To pay download oneapp mobile app or visit www.cubeoneapp.com"
      5 => ""
      6 => "6. Bank Details:- The Mumbai District Central Co-Op. Bank Ltd., A/c No.**************, IFSC Code: MDCB0680018, Branch: Anushaktinagar Branch."
      7 => ""
      8 => "This is a computer generated bill, hence a signature is not required."
    ]
    "showInterestBreakup" => 1
    "showChsoneFooter" => true
    "invoiceFontSize" => "11"
    "showBankDetail" => 1
    "arrBankDetail" => array:19 [
      "account_id" => 4
      "soc_id" => 8485
      "ledger_account_id" => 200
      "account_name" => "NATRAJ CHS LTD"
      "bank_name" => "STATE BANK OF INDIA"
      "branch" => "SECTOR-12 KHARGHAR"
      "account_number" => "***********"
      "bank_address" => "SHOP NO.20 AND 21, GROUND FLOOR AND 21 AND 22 ON 1ST FLOOR, RAHUL APT CHS"
      "bank_city" => "SECTOR-12, KHARGHAR, NAVI MUMBAI"
      "bank_ifsc" => "SBIN0017265"
      "default_account" => 1
      "created_by" => 1066
      "added_on" => "2024-07-22T22:38:59.000000Z"
      "modified_on" => "2024-07-22T22:38:59.000000Z"
      "status" => 1
      "group_id" => 5
      "active_for_payments" => 0
      "default_bank_for_incidental" => 1
      "default_bank_for_nonmember" => 1
    ]
  ]
  "arrSocietyDetail" => array:35 [
    "soc_id" => 8485
    "soc_type_id" => "residential"
    "soc_name" => "Shweta Co op Housing Society Ltd"
    "soc_reg_name" => ""
    "soc_subdomain_name" => "society"
    "soc_reg_num" => ""
    "soc_gst_number" => ""
    "place_of_supply" => null
    "soc_num_of_units" => 0
    "soc_plot_area" => 10
    "soc_address_1" => "1905, Plot No. 4 & 6, Sector 30a"
    "soc_address_2" => "Vashi, Navi Mumbai"
    "soc_landmark" => "Bhiwandi"
    "soc_city_or_town" => "Mumbai"
    "soc_state" => "Maharashtra"
    "soc_pincode" => 4546454
    "added_on" => null
    "modified_on" => "2025-06-25 19:42:36"
    "status" => 1
    "completed" => 1
    "firm_id" => 0
    "firm_name" => ""
    "firm_address" => ""
    "soc_units_added" => 0
    "soc_registration_date" => null
    "api_key" => "780971e3b4204c050ee1e77e2e1638e94c38169e"
    "api_server_key" => "e462cc42e4239eecb985e4529bc2fa1e4c1e2694"
    "soc_signature" => "signature."
    "salt" => "8a72f1ff7e39af2a35b74c7f384c323a2e7b0ba4"
    "prosim_comp_id" => null
    "is_auto_invoice_on" => 0
    "soc_office_email" => ""
    "soc_office_mobile" => ""
    "fsadmin_code" => null
    "soc_name_short" => "suraj"
  ]
  "arrInvoiceType" => array:1 [
    "invoice_type" => array:1 [
      0 => "Preview"
    ]
  ]
  "arrMemberDetail" => []
  "arrLastPeriodPaymentTransaction" => []
  "arrIncomeInvoiceDetail" => []
  "invoice_particular" => array:1 [
    0 => array:3 [
      "particular_name" => "Delayed Payment Charges (0% per year)<br> (Previous Due Amount: 0)"
      "amount" => 0
      "hsn_sac_code" => ""
    ]
  ]
  "rate" => 0
  "intrestPrinciple" => 0
  "interestAmount" => 0
  "invoiceTotalAmount" => 0
  "advanceAmount" => 0
  "grandTotalAmount" => 0
  "outstandingPrincipalAmount" => 0
  "outstandingInterestAmount" => 0
  "balanceDue" => 0
  "rupeesInWord" => 0
  "arrInvoiceGeneralNote" => array:9 [
    0 => "1. Pay your maintenance bill before due date to avoid late payment charges."
    1 => "2. Errors, If any, may please be brought to the notice of the Managing Committee by email within week."
    2 => "3. The due date of payment of Society Charges ( 1st  June for April - June quarter, 1st  September for July - September quarter, 1st December for October - December quarter and 1st  March for January - March quarter AND.  Billing date (1st April for April - June quarter, 1st July for July - September quarter, 1st October for October - December quarter and 1st January for January - March quarter) of payment of Society Charges for the financial year 2022-23 onwards and that the payment received after the due date shall attract an interest @ 21% per annum from the bill date to the actual date of payment. Members paying by cheque must ensure that payment is credited to the bank account of the Society by the due date and that the date of credit to the bank is taken into account and not the cheque deposit date."
    3 => "4. Use convenient payment options to pay your dues through online or cheque."
    4 => "5. To pay download oneapp mobile app or visit www.cubeoneapp.com"
    5 => ""
    6 => "6. Bank Details:- The Mumbai District Central Co-Op. Bank Ltd., A/c No.**************, IFSC Code: MDCB0680018, Branch: Anushaktinagar Branch."
    7 => ""
    8 => "This is a computer generated bill, hence a signature is not required."
  ]
  "arrBankDetail" => array:19 [
    "account_id" => 4
    "soc_id" => 8485
    "ledger_account_id" => 200
    "account_name" => "NATRAJ CHS LTD"
    "bank_name" => "STATE BANK OF INDIA"
    "branch" => "SECTOR-12 KHARGHAR"
    "account_number" => "***********"
    "bank_address" => "SHOP NO.20 AND 21, GROUND FLOOR AND 21 AND 22 ON 1ST FLOOR, RAHUL APT CHS"
    "bank_city" => "SECTOR-12, KHARGHAR, NAVI MUMBAI"
    "bank_ifsc" => "SBIN0017265"
    "default_account" => 1
    "created_by" => 1066
    "added_on" => "2024-07-22T22:38:59.000000Z"
    "modified_on" => "2024-07-22T22:38:59.000000Z"
    "status" => 1
    "group_id" => 5
    "active_for_payments" => 0
    "default_bank_for_incidental" => 1
    "default_bank_for_nonmember" => 1
  ]
]


