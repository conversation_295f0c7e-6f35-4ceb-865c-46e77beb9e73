<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('jobs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('task_id')->constrained('tasks');
            $table->integer('user_id');
            $table->string('email_or_phone');
            $table->string('type');
            $table->string('subject')->nullable();
            $table->text('content');
            $table->string('url')->nullable();
            $table->string('cc_users')->nullable();
            $table->string('bcc_users')->nullable();
            $table->integer('status')->default(0);
            $table->integer('is_failed')->default(0);
            $table->text('failed_reason')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('jobs');
    }
};
