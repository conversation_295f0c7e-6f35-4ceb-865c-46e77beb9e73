<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Purchase Order</title>
  <style>
    body {
      font-family: Arial, sans-serif;
    }

    .invoice {
      width: 100%;
    }

    .container {
      min-height: 950px !important;
      max-width: 1170px;
      margin: 0 auto;
      padding: 15px;
    }

    .invoice-header {
      text-align: center;
      background-color: #394d84;
      padding: 10px 15px;
    }

    .invoice-header h2 {
      color: #FFF;
    }

    .society {
      padding: 15px 0;
      text-align: center;
    }

    .society h4 {
      margin: 0;
      font-size: 14px;
    }

    .society p {
      text-align: center;
      margin-top: 0;
      font-size: 10px;
      max-width: 300px;
      word-wrap: break-word;
      margin: 0 auto;
    }

    .billed-address {
      width: 50%;
      float: left;
      text-align: left;
      line-height: 21px;
      margin: 15px 0;
    }

    .billed-address div {
      padding: 5px 10px;
    }

    .order-tbl {
      width: 50%;
      float: right;
      margin: 15px 0;
    }

    .order-tbl table {
      border-collapse: collapse;
      border-spacing: 0;
      margin-bottom: 0;
      float: right;
      border-width: 1px;
      border-style: solid;
      border-color: #DDDDDD;
      margin: 0px;
    }

    .order-tbl td {
      border-width: 1px;
      border-style: solid;
      border-color: #DDDDDD;
      line-height: 1.42857;
      padding: 8px;
      vertical-align: top;
    }

    #tbl {
      width: 100%;
      border-spacing: 0px;
      margin-top:12rem;
    }

    #tbl thead {
      background-color: #394d84;
    }

    #tbl th {
      color: #FFF;
      text-align: center;
    }

    #tbl td {
      text-align: center;
      border: 1px solid #DDDDDD;
      line-height: 1.42857;
      padding: 8px;
      vertical-align: top;
    }

    #tbl td.alt-background {
      background-color: #e7ebf7;
    }

    #tbl .amount-row {
      text-align: right;
      padding: 5px 0;
      border-bottom: 1px solid #DDDDDD;
    }

    #tbl .amount-row.total {
      padding: 5px 10px;
    }
  </style>
</head>

<body>

  <div class="invoice">
    <div class="container">

      <div class="invoice-header">
        <h2>Purchase Order Details</h2>
      </div>

      <div class="society">
        <h4>{{$data['soc_name'] ?? ''}}</h4>
        <p>{{$data['soc_address'] ?? ''}}</p>
      </div>

      <div class="billed-address">
        <div>
          <span><strong>Title</strong></span>
          <span>{{$data['purchase_form_title'] ?? ''}}</span>
        </div>
        <div>
          <span><strong>Description</strong></span>
          <span>{{$data['purchase_form_desc'] ?? ''}}</span>
        </div>
      </div>

      <div class="order-tbl">
        <table class="table table-bordered">
          <tbody>
            <tr>
              <td><strong>PO No.</strong></td>
              <td>{{$data['purchase_form_po_number'] ?? ''}}</td>
            </tr>
            <tr>
              <td><strong>Vendor Name</strong></td>
              <td>{{$data['purchase_form_vendor_name'] ?? ''}}</td>
            </tr>
            <tr>
              <td><strong>Vendor Email</strong></td>
              <td>{{$data['vendor_email'] ?? ''}}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div>
        <table id="tbl" class="display table-bordered striped basictable" cellspacing="0">
          <thead>
            <tr>
              <th>Item Name</th>
              <th>Unit Cost</th>
              <th>Item Quantity</th>
              <th>Total Cost</th>
            </tr>
          </thead>
          <tbody>
            @if(isset($data['purchase_form_items']) && count($data['purchase_form_items']) > 0)
            @foreach($data['purchase_form_items'] as $item)
            <tr>
              <td>{{$item['item_name'] ?? ''}}</td>
              <td>{{$item['item_cost'] ?? ''}}</td>
              <td class="alt-background">{{$item['item_quantity'] ?? ''}}</td>
              <td class="alt-background">{{$item['total_cost'] ?? ''}}</td>
            </tr>
            @endforeach
            @endif
            <tr>
              <td colspan="3" class="amount-row"><strong>Amount</strong></td>
              <td class="amount-row total"><i class="fa fa-inr"></i>{{$data['purchase_form_amount'] ?? ''}}</td>
            </tr>
            <tr>
              <td colspan="3" class="amount-row"><strong>Approved by</strong></td>
              <td class="amount-row total">{{$data['approved_by'] ?? ''}}</td>
            </tr>
            <tr>
              <td colspan="3" class="amount-row"><strong>Pending Approval</strong></td>
              <td class="amount-row total">{{$data['approval_pending_by_name'] ?? ''}}</td>
            </tr>
            <tr>
              <td colspan="3" class="amount-row"><strong>Rejected by</strong></td>
              <td class="amount-row total">{{$data['reviewed_pending_by_name'] ?? '-'}}</td>
            </tr>
          </tbody>
        </table>
      </div>

    </div>
  </div>

</body>
</html>
