<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use PDF;

class GenerateInvoice extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:generate-invoice';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command is used to generate invoice for the user.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $pdf = PDF::loadView('email.oneSociety-invoice', []);
        return $pdf->stream('invoice.pdf');
    }
}
