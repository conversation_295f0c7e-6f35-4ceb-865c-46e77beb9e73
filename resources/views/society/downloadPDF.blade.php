<html>
<head>
    <title>Download PDF</title>
    <style>
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .table table {
            width: 100%;
            border-collapse: collapse;
        }

        .table table thead th {
            font-size: 11px;
            padding: 10px;
            border: 1px solid #3a3a3a;
            background-color: #ddd;
        }

        .table table tr td {
            font-size: 11px;
            padding: 10px;
            border: 1px solid #3a3a3a;
            text-align: right;
        }
        @font-face {
            font-family: 'DejaVu Sans';
            src: url('{{ storage_path("fonts/DejaVuSans.ttf") }}') format('truetype');
            font-weight: normal;
            font-style: normal;
        }
        tbody {
            font-family: 'DejaVu Sans', sans-serif;
        }
    </style>
</head>
<body>
        <div class="header">
        @if(isset($header) && is_array($header))
    <h1>{{ $header['society_name'] ?? 'N/A' }}</h1>
    <p>{{ $header['society_reg_num'] ?? 'N/A' }}</p>
@else
    <h1>N/A</h1>
    <p>N/A</p>
@endif
        </div>
    <div class="table">
        <table>
            <thead>
                <tr>
                    @foreach($headings as $heading)
                        <th>{{ ucwords(preg_replace("/[_|\/]/", " ", preg_replace('/[^A-Za-z0-9_ ]/', '', strtolower($heading)))) }}</th>
                    @endforeach
                </tr>
            </thead>
            <tbody>
                @foreach($data as $row)
                    <tr>
                        @foreach($row as $key => $value)
                            <td>{{ $value }}</td>
                        @endforeach
                    </tr>
                @endforeach
            </tbody>
        </table>
        @php
            $isEmpty = array_reduce($summaryHeadings, function ($carry, $item) {
                return $carry && $item === "";
            }, true);
        @endphp
        @if($isEmpty)
            <table>
                <h4>Summary</h4>
                <thead>
                    <tr>
                        @foreach($summaryHeadings as $summaryHeading)
                            <th>{{ ucwords(preg_replace("/[_|\/]/", " ", preg_replace('/[^A-Za-z0-9_ ]/', '', strtolower($summaryHeading)))) }}</th>
                        @endforeach
                    </tr>
                </thead>
                <tbody>
                    @foreach($data as $row)
                        <tr>
                            @foreach($summaryHeadings as $summaryHeading)
                                <td>{{ $row[$summaryHeading] }}</td>
                            @endforeach
                        </tr>
                    @endforeach
                </tbody>
            </table>
        @endif
    </div>
</body>
</html>
