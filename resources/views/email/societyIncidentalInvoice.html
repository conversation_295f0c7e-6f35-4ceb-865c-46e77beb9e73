<html>
    <head>
        <title>Invoice</title>
        <style>

            .container{
                width: 100%;
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
                border: 1px solid #ccc;
            }
            .header {
                margin-top: 3%;
                text-align: center;
            }

            .header img {
                margin: 0 auto 20px auto;
                display: block;
            }
            
            .header h1 {
                font-size: 18px;
                font-weight: 600;
                margin: 0;
                margin-bottom: 10px;
            }

            .header p {
                font-size: 11px;
                margin: 0;
                font-weight: 600;
                line-height: 1.5;
            }

            .to-details {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-top: 2%;
            }

            .to-details .left {
                width: 65%;
            }

            .to-details .right {
                width: 35%;
            }

            .to-details p {
                font-size: 11px;
                margin: 0;
                line-height: 1.5;
            }

            .to-details p span {
                font-weight: 600;
            }

            .to-details .left p span:first-child {
                width: 16%;
                display: inline-block;
            }

            .to-details .right p span:first-child {
                width: 35%;
                display: inline-block;
            }

            .table {
                margin-top: 2%;
            }

            .table table {
                width: 100%;
                border-collapse: collapse;
            }

            .table table thead th {
                font-size: 11px;
                padding: 10px;
                border: 1px solid #3a3a3a;
                background-color: #ddd;
            }

            .table table tr td {
                font-size: 11px;
                padding: 10px;
                border: 1px solid #3a3a3a;
                text-align: right;
            }

            .table table tr td span {
                font-weight: 600;
            }

            .table table tr td p {
                font-weight: 600;
                margin: 0;
            }

            .table table tr td div{
                margin-top: 15px;
            }

            .table table tr td div p {
                margin: 3px auto;
            }

            .table table thead th:first-child, .table table tbody td:first-child {
                text-align: left;
            }

            /* .table table tr td:first-child {
                width: 40%;
            }

            .table table tr td:last-child {
                width: 20%;
            } */

            .table table th right, .table table tr td right {
                float: right;
            }

            .notes {
                font-size: 11px;
                font-weight: 600;
            }

            .notes p{
                margin: 2px auto;
            }

            .notes ul {
                margin: 0;
                padding: 0;
                list-style: none;
                list-style-type: none; /* Remove the default bullet points */
                counter-reset: list-counter;
            }

            ul li::before {
                counter-increment: list-counter; /* Increment the counter for each list item */
                content: counter(list-counter)"."; /* Display the current counter value before the list item */
                margin-right: 5px; /* Add some spacing between the number and the list item text */
                font-weight: bold; /* Optionally, make the numbers bold */
            }

            .signature {
                margin-top: 3%;
                text-align: right;
                font-size: 12px;
            }

            .sign span{
                margin-top: 30px;
                width: 30%;
                display: inline-block;
                height: 1px;
                background-color: #3a3a3a;
            }

            .ticket-card{
                margin-top: 20px;
                display: inline-block;
                padding: 20px 15px 5px 15px;
                border: 1px dashed #3a3a3a;
                background-color: #efeeee;
                border-bottom-left-radius: 20px;
                border-top-right-radius: 20px;
            }
        </style>
    </head>
    <body>

        <div class="container">

            <div class="header">
                <img src="https://society.cubeonebiz.com/images/one-society.png" alt="logo" width="100px">
                <h1>Cyber One Premises Co-Operative Society Ltd</h1>
                <p>Reg No: AKKI1234567890</p>
                <p>Cyber One, Plot-4 & 6, Sector-30a, Vashi, Near Odissa Bhavan, Navi Mumbai, Maharashtra, 400703</p>
            </div>

            <div class="to-details">
                <div class="left">
                    <p><span>To,</span></p>
                    <p><span>Name:</span><span>Greenscape Developers Pvt Ltd</span></p>
                    <p><span>Unit Number:</span>Cyber One/1905</p>
                    <p><span>GSTIN/UIN:</span>27AACCG4829L1ZA</p>
                </div>
                <div class="right">
                    <p></p>
                    <p>
                        <span>Invoice No:</span>
                        # INV00148 (Original)
                    </p>
                    <p>
                        <span>Invoice Date:</span>
                        Oct 01, 2018
                    </p>
                    <p>
                        <span>Due Date:</span>
                        Oct 31, 2018
                    </p>
                    <p>
                        <span>Invoicing Period:</span>
                        Oct 01, 2018 To Mar 31, 2019
                    </p>
                </div>
            </div>

            <div class="table">
                <table>
                    <thead>
                        <th colspan="2">Particulars</th>
                        <th><right>Amount (RS.)</right></th>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="2">Maintenance Fee</td>
                            <td>57360.00</td>
                        </tr>
                        <tr>
                            <td colspan="2">Delayed Payment Charges (0.00)</td>
                            <td>0.00</td>
                        </tr>
                        <tr>
                            <td rowspan="7">
                                <p>E-collect (VPA) Pay Via Netbanking:</p>
                                <div>
                                    <p>Account Name : CYBER ONE PREMISES CO OPERATIVE SOCIETY LTD.</p>
                                    <p>Account Number : CHSONECYBEROCO1905</p>
                                    <p>Account Type : Current</p>
                                    <p>IFSC :YESB0CMSNOC</p>

                                    <span class="ticket-card">Please Pay Rs. 777.00/-</span>

                                </div>
                            </td>
                            <td><span><right>Invoice Total:</right></span></td>
                            <td><span>67684.80</span></td>
                        </tr>

                        <tr>
                            <td><right>Advance Amount:</right></td>
                            <td>(-) 0.00</td>
                        </tr>

                        <tr>
                            <td><span><right>Grand Total:</right></span></td>
                            <td><span>67684.80</span></td>
                        </tr>

                        <tr>
                            <td><right>Principal Arrears:</right></td>
                            <td>(+) 312612.00</td>
                        </tr>
                        <tr>
                            <td><right>Interest Arrears:</right></td>
                            <td>(+) 0.00</td>
                        </tr>

                        <tr>
                            <td><span><right>Balance Due:</right></span></td>
                            <td><span>67684.80</span></td>
                        </tr>
                        <tr>
                            <td colspan="2">
                                <right><span>Amount In Words:</span> Three Lakh Eighty Thousand Two Hundred Ninety Six Rupees Eighty Paise</right>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="notes">
                <p>Note: <span>Before May 30, 2020</span></p>
            </div>

            <div class="signature">
                <p>For Cyber One Premises Co-Operative Society Ltd</p>
                <p class="sign">Authorised Signatory<span> </span></p>
            </div>

        </div>

    </body>
</html>