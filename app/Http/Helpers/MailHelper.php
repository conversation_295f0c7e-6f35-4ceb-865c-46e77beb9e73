<?php

namespace App\Http\Helpers;

use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\Exception;

class MailHelper {

    public static function send_phpmailer( $from, $to, $cc, $bcc, $subject, $content_message, $encoding='utf-8', $files=array() ) {
        global $config;
        $mailer_config = self::getConfiguration();
        $hasAttachment= (is_array($files) && !empty($files)) ? true : false;
        
        $mail = new PHPMailer();
        $mail->IsSMTP();                                      // Set mailer to use SMTP
        $mail->Host = !empty($mailer_config['host'])?$mailer_config['host']:'email-smtp.ap-south-1.amazonaws.com'; #$mailer_config['host'];  // Specify main and backup server
        $mail->SMTPAuth = true;                               // Enable SMTP authentication
        $mail->Username = !empty($mailer_config['connection_config']['username'])?$mailer_config['connection_config']['username']:'AKIARRSRIRX2WZGQYB7J'; #$mailer_config['connection_config']['username'];                            // SMTP username
        $mail->Password = !empty($mailer_config['connection_config']['password'])?$mailer_config['connection_config']['password']:'BOLPtocopG5HQy1okGnhbeOsgk05mUeBnFb4VVFSmbAK'; #$mailer_config['connection_config']['password'];                           // SMTP password
        $security_level = (isset($mailer_config['security']) ? $mailer_config['security'] : '');
        $mail->SMTPSecure = 'tls';                            // Enable encryption, 'ssl' also accepted
        $mail->Port       = '587';                                    //Set the SMTP port number - 587 for authenticated TLS
        $mail->SMTPDebug  = 1;

        if( is_array($from) && !empty($from) ) {
            $from_address_array = $mailer_config['from_email_address'];
            foreach($from as $name => $email) {
                if( isset($from_address_array[$name]) ) {
                    $mail->SetFrom($from_address_array[$name], $name);break;
                    // $mail->SetFrom( $mailer_config['from_email'], $mailer_config['from_name']);break;
                }
                else {
                    $mail->SetFrom( $mailer_config['from_email'], $mailer_config['from_name']);break;
                }
            }
        }
        else {
            $mail->SetFrom( $mailer_config['from_email'], $mailer_config['from_name']);
        }

        if( is_array($to) && !empty($to) ) {
            foreach($to as $name => $email) {
                $mail->AddAddress($email, $name);
                // $emails =  explode(',', $email);
                // foreach ($emails as $value) {
                //     $mail->AddAddress($value, $name);
                // }
            }
        }

        if( is_array($cc) && !empty($cc) ) {
            foreach($cc as $name => $email) {
                $mail->AddCC($email, $name);
            }
        }

        if( is_array($bcc) && !empty($bcc) ) {
            foreach($bcc as $name => $email) {
                $mail->AddBCC($email, $name);
            }
        }

        $mail->WordWrap = 50;                                 // Set word wrap to 50 characters
        if( is_array($files) && !empty($files) ) {
            foreach($files as $itr => $full_file_path) {
                $mail->AddAttachment( $full_file_path, basename($full_file_path) );
            }#end of foreach
        }else if(is_string($files) && !empty($files)){
            $files = "https://society.chsone.in" . $files;

            // dd($files);

            // dd($files);
            $file = file_get_contents($files);

            // get the file name and extension
            $file_name = basename($files);

            $mail->AddStringAttachment($file, $file_name);

            // $mail->AddAttachment($files); // Add attachments
        } 
                
        $mail->IsHTML(true);

        $mail->Subject = $subject;
        $mail->Body    = trim($content_message);
        $mail->AltBody = trim(strip_tags(stripslashes($content_message)));

        // return $mail;
        // dd($mail);
        try {
            if(!$mail->Send()) {           
                return $result = array('status'=>0, 'error'=>$mail->ErrorInfo);
            }
            else{
                return $result = array('status'=>1);
            }
        } catch(\Exception $e) {
                //    echo $e->getCode().' :: '.print_r($e->getMessage(), true);
        }
        unset($mail);
    }

    public static function getConfiguration() {
        $config = array();
        $config['from_name'] = 'ONEAPP';
        $config['from_email'] = '<EMAIL>';
        $config['from_email_address'] = '<EMAIL>';
        $config['host'] = 'email-smtp.ap-south-1.amazonaws.com';
        $config['port'] = 587;
        $config['security'] = 'tls';
        $config['connection_config']['username'] = 'AKIARRSRIRX2WZGQYB7J';
        $config['connection_config']['password'] = 'BOLPtocopG5HQy1okGnhbeOsgk05mUeBnFb4VVFSmbAK';
        return $config;
    }

}