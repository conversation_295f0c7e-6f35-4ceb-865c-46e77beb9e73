<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $society_name ?? 'Society Report' }}</title>
    <link rel="shortcut icon" href="https://stgsociety.chsone.in/images/favicon.png" />
    <style>
        html { font-size: 62.5%; margin: 0; }
        body { font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; font-size: 12px; margin: 0; color: #333; background: #fff; }
        table { background: white; border: solid 1px #ddd; width: 100%; margin: 20px 0; border-collapse: collapse; }
        table th, table td { padding: 8px; border: 1px solid #ddd; text-align: left; font-size: 11px; }
        table thead { background-color: #f0f0f0; }
        .printOption { background: #001941; height: 40px; padding: 10px; color: #fff; }
        .footer { background: #001941; padding: 10px; color: #fff; text-align: right; }
        .section-header { font-weight: bold; text-align: left; background: #f9f9f9; font-size: 1.1em; }
        .main-title { text-align: center; font-size: 1.6em; font-weight: bold; margin-top: 20px; }
        .sub-title { text-align: center; font-size: 1.2em; margin-bottom: 20px; color: #666; }
        .report-info { text-align: center; margin-bottom: 20px; }
        .report-info div { margin: 5px 0; }
        .unit-info { text-align: center; margin-bottom: 20px; font-weight: bold; }
        .summary-section { margin-top: 30px; }
        .summary-title { font-weight: bold; font-size: 1.3em; margin-bottom: 15px; }
        .summary-table { background-color: #f9f9f9; }
        .summary-table th, .summary-table td { background-color: #f9f9f9; font-weight: bold; }
        .total-row { background-color: #e9e9e9; font-weight: bold; }
        .report-date { text-align: left; margin-top: 20px; font-weight: bold; }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        .small-font { font-size: 10px; }
        .opening-balance-row { background-color: #f8f9fa; }
    </style>
</head>
<body>
    <div class="printOption">
        <p>{{ 'Member Unit Statement Report' ?? 'Society Report' }}</p>
    </div>
    
    <div class="main-title">{{ $company_details['society_name'] ?? 'SOCIETY NAME' }}</div>
    
    @if(isset($reg_number) || isset($gstin))
    <div class="report-info">
        @if(isset($reg_number))
        <div>Reg No: {{ $reg_number }}</div>
        @endif
        @if(isset($gstin))
        <div>GSTIN/UIN: {{ $gstin }}</div>
        @endif
    </div>
    @endif
    
    <div class="sub-title">Member Unit Statement Report Summary</div>
    
    @if(isset($unit_id))
    <div class="unit-info">
        Unit_Id: {{ $unit_id }}
    </div>
    @endif
    
    @if(isset($date_range))
    <div class="text-center">
        <strong>Date: {{ $date_range }}</strong>
    </div>
    @endif
    
    <div class="tableDiv" style="width: 98%; margin: 0 auto;">
        @if(isset($data) && is_array($data) && count($data) > 0)
            @php
                $reportData = $data[0] ?? [];
                $summaryData = $data[1] ?? [];
                
                // Define custom order for columns based on screenshot
                $orderedHeaders = [
                    'date',
                    'type',
                    'reference_no',
                    'payment_reference',
                    'debit',
                    'credit',
                    'balance'
                ];
                
                // Get headers from first row and order them
                $headers = [];
                if (!empty($reportData)) {
                    $firstRow = reset($reportData);
                    if (is_array($firstRow)) {
                        $allHeaders = array_keys($firstRow);
                        
                        // Filter and order headers based on what exists in data
                        foreach ($orderedHeaders as $orderedHeader) {
                            if (in_array($orderedHeader, $allHeaders)) {
                                $headers[] = $orderedHeader;
                            }
                        }
                        
                        // Add any remaining headers that weren't in our ordered list
                        foreach ($allHeaders as $header) {
                            if (!in_array($header, $headers) && !in_array($header, ['id', 'unit_id', 'created_at', 'updated_at'])) {
                                $headers[] = $header;
                            }
                        }
                    }
                }
            @endphp
            
            @if(!empty($headers) && !empty($reportData))
            <table>
                <thead>
                    <tr>
                        @foreach($headers as $header)
                            <th class="small-font">
                                @switch($header)
                                    @case('date')
                                        Date
                                        @break
                                    @case('type')
                                        Type
                                        @break
                                    @case('reference_no')
                                        Reference No
                                        @break
                                    @case('payment_reference')
                                        Payment Reference
                                        @break
                                    @case('debit')
                                        Debit
                                        @break
                                    @case('credit')
                                        Credit
                                        @break
                                    @case('balance')
                                        Balance
                                        @break
                                    @default
                                        {{ ucwords(str_replace('_', ' ', $header)) }}
                                @endswitch
                            </th>
                        @endforeach
                    </tr>
                </thead>
                <tbody>
                    @foreach($reportData as $row)
                        <tr class="{{ (isset($row['type']) && str_contains(strtolower($row['type']), 'opening')) ? 'opening-balance-row' : '' }}">
                            @foreach($headers as $header)
                                <td class="{{ in_array($header, ['debit', 'credit', 'balance']) ? 'text-right small-font' : 'small-font' }}">
                                    @if(in_array($header, ['debit', 'credit', 'balance']) && is_numeric($row[$header]))
                                        {{ number_format($row[$header], 2) }}
                                    @elseif($header === 'date' && $row[$header])
                                        {{ date('Y-m-d', strtotime($row[$header])) }}
                                    @elseif($header === 'type')
                                        {{ ucfirst($row[$header] ?? '') }}
                                    @elseif($header === 'payment_reference' && empty($row[$header]))
                                        NA
                                    @else
                                        {{ $row[$header] ?? '' }}
                                    @endif
                                </td>
                            @endforeach
                        </tr>
                    @endforeach
                </tbody>
            </table>
            @endif
        @endif
    </div>
    
    {{-- Summary Section - Debug and flexible handling --}}
    @if(isset($data) && is_array($data) && count($data) > 1)
        @php
            $summaryData = null;

            // Handle different summary data structures
            if (is_array($data[1])) {
                if (isset($data[1][0]) && is_array($data[1][0])) {
                    // If data[1] is array of arrays, get first element
                    $summaryData = $data[1][0];
                } else {
                    // If data[1] is direct array
                    $summaryData = $data[1];
                }
            }

            // Debug: Check what keys are available
            $availableKeys = $summaryData ? array_keys($summaryData) : [];
        @endphp



        @if($summaryData && !empty($summaryData))
        <div class="summary-section">
            <div class="summary-title">Summary</div>
            <table class="summary-table">
                <thead>
                    <tr>
                        {{-- Use the actual field names from your data --}}
                        @if(isset($summaryData['debit_sum']))
                        <th class="text-center">Total Debit</th>
                        @endif
                        @if(isset($summaryData['credit_sum']))
                        <th class="text-center">Total Credit</th>
                        @endif
                        @if(isset($summaryData['total_sum']))
                        <th class="text-center">Total (Debit-Credit)</th>
                        @endif
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        @if(isset($summaryData['debit_sum']))
                        <td class="text-center">{{ number_format($summaryData['debit_sum'], 2) }}</td>
                        @endif

                        @if(isset($summaryData['credit_sum']))
                        <td class="text-center">{{ number_format($summaryData['credit_sum'], 2) }}</td>
                        @endif

                        @if(isset($summaryData['total_sum']))
                        <td class="text-center">{{ number_format($summaryData['total_sum'], 2) }}</td>
                        @endif
                    </tr>
                </tbody>
            </table>
        </div>

        {{-- Debug information (remove in production) --}}
        {{--
        <div style="margin-top: 20px; padding: 10px; background: #f0f0f0; font-size: 10px;">
            <strong>Debug - Available Summary Keys:</strong> {{ implode(', ', $availableKeys) }}
        </div>
        --}}
        @endif
    @endif
    
    @if(isset($report_date))
    <div class="report-date">
        Report date: {{ $report_date }}
    </div>
    @endif
    
    <div class="footer">
        <span>Powered By:</span> <img src="https://stgsociety.chsone.in/images/logo.png" alt="CHSONE" style="vertical-align: middle;">
    </div>
</body>
</html>
