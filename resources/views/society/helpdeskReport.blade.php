<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $society_name ?? 'Helpdesk Report' }}</title>
    <link rel="shortcut icon" href="https://stgsociety.chsone.in/images/favicon.png" />
    <style>
        html { font-size: 62.5%; margin: 0; }
        body { font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; font-size: 12px; margin: 0; color: #333; background: #fff; }
        table { background: white; border: solid 1px #ddd; width: 100%; margin: 20px 0; border-collapse: collapse; }
        table th, table td { padding: 6px; border: 1px solid #ddd; text-align: left; font-size: 11px; }
        table thead { background-color: #f0f0f0; }
        .printOption { background: #001941; height: 40px; padding: 10px; color: #fff; }
        .footer { background: #001941; padding: 10px; color: #fff; text-align: right; }
        .section-header { font-weight: bold; text-align: left; background: #f9f9f9; font-size: 1.1em; }
        .main-title { text-align: center; font-size: 1.6em; font-weight: bold; margin-top: 20px; }
        .sub-title { text-align: center; font-size: 1.2em; margin-bottom: 20px; color: #666; }
        .report-info { text-align: center; margin-bottom: 20px; }
        .report-info div { margin: 5px 0; }
        .summary-section { margin-top: 30px; }
        .summary-title { font-weight: bold; font-size: 1.3em; margin-bottom: 15px; }
        .summary-table { background-color: #f9f9f9; }
        .summary-table th, .summary-table td { background-color: #f9f9f9; font-weight: bold; }
        .total-row { background-color: #e9e9e9; font-weight: bold; }
        .report-date { text-align: left; margin-top: 20px; font-weight: bold; }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        .small-font { font-size: 10px; }
    </style>
</head>
<body>
    <div class="printOption">
        <p>{{ 'Helpdesk Report' ?? 'Society Report' }}</p>
    </div>

    <div style="width:100%;text-align:center;padding:10px;">
        <h2 style="margin-bottom:0;margin-top:5px">{{$company_details['society_name'] ?? 'Society Name'}}</h2>
        <h3 style="margin-bottom:0;margin-top:5px">[REGN.NO:{{$company_details['society_reg_num'] ?? 'Registration Number'}}]</h3>
        <p style="margin-bottom:0;margin-top:5px">{{$company_details['society_address'] ?? 'Society Address'}}</p>
    </div>

    <div class="sub-title">Complaints</div>

    <div class="text-center">
        <strong>Search: All Filters: All</strong>
    </div>

    <div class="tableDiv" style="width: 98%; margin: 0 auto;">
        @if(isset($data) && is_array($data) && count($data) > 0)
            @php
                $ticketData = $data[0] ?? [];
                $summaryData = $data[1][0] ?? [];
            @endphp

            <table>
                <thead>
                    <tr>
                        <th class="small-font">Ticket No.</th>
                        <th class="small-font">Date</th>
                        <th class="small-font">Subject</th>
                        <th class="small-font">From</th>
                        <th class="small-font">Assigned to</th>
                        <th class="small-font">Priority</th>
                        <th class="small-font">Status</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($ticketData as $row)
                        <tr>
                            <td class="small-font">{{ $row['ticket_number'] ?? '' }}</td>
                            <td class="small-font">{{ $row['created_date'] ?? '' }}</td>
                            <td class="small-font">{{ $row['title'] ?? '' }}</td>
                            <td class="small-font">{{ $row['raised_by_name'] ?? '' }}</td>
                            <td class="small-font">{{ $row['member_email_id'] ?? '' }}</td>
                            <td class="small-font">{{ ucfirst($row['priority'] ?? '') }}</td>
                            <td class="small-font">{{ ucfirst($row['status'] ?? '') }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>

            {{-- Summary Section --}}
            <div class="summary-section">
                <div class="summary-title">Summary</div>
                <table class="summary-table">
                    <thead>
                        <tr>
                            <th class="text-center">Open</th>
                            <th class="text-center">Closed</th>
                            <th class="text-center">On Hold</th>
                            <th class="text-center">Resolved</th>
                            <th class="text-center">Reopened</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="total-row">
                            <td class="text-center">{{ $summaryData['open'] ?? 0 }}</td>
                            <td class="text-center">{{ $summaryData['closed'] ?? 0 }}</td>
                            <td class="text-center">{{ $summaryData['on_hold'] ?? 0 }}</td>
                            <td class="text-center">{{ $summaryData['resolved'] ?? 0 }}</td>
                            <td class="text-center">{{ $summaryData['reopened'] ?? 0 }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        @else
            <div class="text-center" style="padding: 20px;">
                <p>No data available for the selected criteria.</p>
            </div>
        @endif
    </div>

    <div class="report-date">
        Report date: {{ $report_date ?? date('d/m/Y') }}
    </div>

    <div class="footer">
        <span>Powered By:</span> <img src="https://stgsociety.chsone.in/images/logo.png" alt="CHSONE" style="vertical-align: middle;">
    </div>
</body>
</html>
