<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DOwnload Invoice Blade</title>
</head>
<body>
    
</body>
</html>
    @php
        if(!empty($arrInvoiceSetting['showInvoiceDetail'])){
            $count = count($arrInvoiceType['invoice_type']) - 1;
        
            foreach($arrInvoiceType['invoice_type'] as $key => $invoice_type){
                $totalInvoiceCount = 0;
                foreach($arrAllUnitInvoiceDetail as $unitKey=>$eachUnitDetail) { 
                    if(!empty($arrInvoiceType) && strtolower($arrInvoiceType['invoice_type'][0])=='preview'){ 
    @endphp

                        <div id="watermark">
                            <p style="position: fixed;bottom:10cm;left:0cm; right: 0cm; margin: auto; font-size:3cm; text-align: center; z-index:-1000; color: rgba(0,0,0,0.05);">PREVIEW</p>
                        </div>
    @php 
        }
        if(!empty($eachUnitDetail['arrIncomeInvoiceDetail'][0]['status']) && strtolower($eachUnitDetail['arrIncomeInvoiceDetail'][0]['status']) == 'cancelled'){ 
    @endphp
<div>
    <p style="position: fixed;bottom:10cm;left:0cm; right: 0cm; margin: auto; font-size:3cm; text-align: center; z-index:-1000; color: rgba(255, 90, 90, 0.35);">CANCELLED</p>
</div>
    @php 
        }
        if($eachUnitDetail['error']){ 
            $arrInvoiceSetting['invoiceFontSize'] = empty($arrInvoiceSetting['invoiceFontSize']) ? 13 : $arrInvoiceSetting['invoiceFontSize'];
    @endphp
<div style="width:100%;margin-top:0px;margin-bottom:0px;margin-right:0px;margin-left:0px;padding-top:0px;padding-bottom:0px;padding-right:0px;padding-left:0px;font-size:<?php echo $arrInvoiceSetting['invoiceFontSize']; ?>px;" >
    <div style="margin-top:0;margin-bottom:0;margin-right:10px;margin-left:10px;padding-left:0px;padding-right:0px;" >        
        <div class="invoice-main" style="padding-bottom: 50px;">
            <div style="width:100%;text-align:center;margin-top:2px;">  
                @if(!empty($arrInvoiceSetting['imageLogoUrl']))
                <img src="<?= $arrInvoiceSetting['imageLogoUrl']; ?>" alt="complex logo" style="max-height: 60px;" /> <br>
                @endif
                <h2 style="margin-bottom: 0;margin-top: 0px;">
                                    @php echo (!empty(trim($arrSocietyDetail['soc_name'])))? ($arrSocietyDetail['soc_name']):''; @endphp</h2>
                <div style="width:100%;text-align:center;line-height:1.5;color:#000000;margin-top:5px;margin-bottom:15px;margin-right:0px;margin-left:0px;font-size: <?php echo $arrInvoiceSetting['invoiceFontSize']; ?>px;">
                                @php echo (!empty($arrSocietyDetail['soc_reg_num'])) ? 'Reg No: '.$arrSocietyDetail['soc_reg_num'] : '';
                                echo (!empty($arrSocietyDetail['soc_gst_number'])) ? ' | GSTIN/UIN: '.$arrSocietyDetail['soc_gst_number'].'<br> ' : '<br>';
                                @endphp
                                @php $strAddress = (!empty($arrSocietyDetail['soc_address_1'])) ? $arrSocietyDetail['soc_address_1'].', ' : ''; 
                                    $strAddress .=  (!empty($arrSocietyDetail['soc_address_2'])) ? $arrSocietyDetail['soc_address_2'].', ' : ''; 
                                    $strAddress .=   (!empty($arrSocietyDetail['soc_landmark'])) ? $arrSocietyDetail['soc_landmark'].', ' : '';
                                    $strAddress .=   (!empty($arrSocietyDetail['soc_city_or_town'])) ? $arrSocietyDetail['soc_city_or_town'].', ' : '';
                                    $strAddress .=   (!empty($arrSocietyDetail['soc_state'])) ? $arrSocietyDetail['soc_state'].', ' : '';
                                    $strAddress .=   (!empty($arrSocietyDetail['soc_state'])) ? $arrSocietyDetail['soc_pincode'].'.' : '';
                                    echo rtrim($strAddress, ', ');
                                ?>
                                @php $strContact = (!empty($arrSocietyDetail['soc_office_email']) ) ? '<br>Email: '.$arrSocietyDetail['soc_office_email'] : ''; @endphp
                                @php $strContact .= (!empty($strContact) && !empty($arrSocietyDetail['soc_office_mobile'])) ? ' | Phone: '.$arrSocietyDetail['soc_office_mobile'] : ''; @endphp
                                @php echo $strContact .= (empty($strContact) && !empty($arrSocietyDetail['soc_office_mobile'])) ? 'Phone: '.$arrSocietyDetail['soc_office_mobile'] : ''; @endphp
                </div> 
            </div>
        </div>
        <div>@php echo $eachUnitDetail['message']; @endphp</div>
    </div>
</div>
        @php 
    }else{
        $eachUnitDetail['defaultColumnCount'] = $eachUnitDetail['originalColumnCount'];
    @endphp
<div style="width:100%;margin-top:0px;margin-bottom:0px;margin-right:0px;margin-left:0px;padding-top:0px;padding-bottom:0px;padding-right:0px;padding-left:0px;font-size:<?php echo $arrInvoiceSetting['invoiceFontSize']; ?>px;" >
    <div style="margin-top:0;margin-bottom:0;margin-right:10px;margin-left:10px;padding-left:0px;padding-right:0px;" >        
        <div class="invoice-main" style="padding-bottom: 50px;">
            <div style="width:100%;text-align:center;margin-top:2px;">  
                                @if(!empty($arrInvoiceSetting['imageLogoUrl']))
                <img src="<?php echo $arrInvoiceSetting['imageLogoUrl']; ?>" alt="complex logo" style="max-height: 60px;" /> <br>
                                @endif
                <h2 style="margin-bottom: 0;margin-top: 0px;">
                        @php echo (!empty(trim($arrSocietyDetail['soc_name'])))? ($arrSocietyDetail['soc_name']):'';?></h2>
                <div style="width:100%;text-align:center;line-height:1.5;color:#000000;margin-top:5px;margin-bottom:15px;margin-right:0px;margin-left:0px;font-size: <?php echo $arrInvoiceSetting['invoiceFontSize']; ?>px;">
                    <strong>
                    <?php echo (!empty($arrSocietyDetail['soc_reg_num'])) ? 'Reg No: '.$arrSocietyDetail['soc_reg_num'] : '';
                                        echo (!empty($arrSocietyDetail['soc_gst_number'])) ? ' | GSTIN/UIN: '.$arrSocietyDetail['soc_gst_number'].'<br> ' : '<br>';?>
                                        <?php $strAddress = (!empty($arrSocietyDetail['soc_address_1'])) ? $arrSocietyDetail['soc_address_1'].', ' : ''; 
                                            $strAddress .=  (!empty($arrSocietyDetail['soc_address_2'])) ? $arrSocietyDetail['soc_address_2'].', ' : ''; 
                                            $strAddress .=   (!empty($arrSocietyDetail['soc_landmark'])) ? $arrSocietyDetail['soc_landmark'].', ' : '';
                                            $strAddress .=   (!empty($arrSocietyDetail['soc_city_or_town'])) ? $arrSocietyDetail['soc_city_or_town'].', ' : '';
                                            $strAddress .=   (!empty($arrSocietyDetail['soc_state'])) ? $arrSocietyDetail['soc_state'].', ' : '';
                                            $strAddress .=   (!empty($arrSocietyDetail['soc_state'])) ? $arrSocietyDetail['soc_pincode'].'.' : '';
                                            echo rtrim($strAddress, ', ');
                                        ?>
                                        <?php $strContact = (!empty($arrSocietyDetail['soc_office_email']) ) ? '<br>Email: '.$arrSocietyDetail['soc_office_email'] : ''; ?>
                                        <?php $strContact .= (!empty($strContact) && !empty($arrSocietyDetail['soc_office_mobile'])) ? ' | Phone: '.$arrSocietyDetail['soc_office_mobile'] : ''; ?>
                                        <?php echo $strContact .= (empty($strContact) && !empty($arrSocietyDetail['soc_office_mobile'])) ? 'Phone: '.$arrSocietyDetail['soc_office_mobile'] : ''; ?>
                    </strong>
                </div> 
            </div>
            <div style="clear:both;" ></div>
                                
                @php  
                    if(!empty($eachUnitDetail['arrIncomeInvoiceDetail'])){
                    foreach($eachUnitDetail['arrIncomeInvoiceDetail'] as $eachInvoiceDetail) {
                @endphp
            <div style="width:50%;float:left; margin-top:0px;" >
                <table style="border-collapse:collapse;border-spacing:0;margin-bottom:10px;float:left;border-color:#646464;" >
                    <tbody>
                        <tr>
                            <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;" ><strong>To,</strong></td>
                        </tr>
                        <tr>
                            <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;" ><strong><?php echo (!empty($arrInvoiceGeneralSetting['INCOME_MAINTENANCE_NAME_LABEL'])) ? $arrInvoiceGeneralSetting['INCOME_MAINTENANCE_NAME_LABEL'].':' : 'Name:' ?></strong></td>
                            <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;" ><strong><?php 
                                                                if(!empty($eachInvoiceDetail['bill_to']))
                                                                {
                                                                   echo ucwords($eachInvoiceDetail['bill_to']);
                                                                }
                                                                else
                                                                {
                                                                   echo (!empty($eachUnitDetail['arrMemberDetail']['member_first_name']))? ucwords($eachUnitDetail['arrMemberDetail']['member_first_name'].' '.$eachUnitDetail['arrMemberDetail']['member_last_name']):'';
                                                                   echo (!empty($eachUnitDetail['associateMembers'])) ? $eachUnitDetail['associateMembers'] : '';
                                                                }
                                                                ?></strong></td>
                        </tr>
              <?php if(empty($eachUnitDetail['hideParkingUnit']) && empty($arrInvoiceGeneralSetting['INCOME_MAINTENANCE_HIDE_PARKING_UNIT'])){?>
                        <tr>
                            <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;" ><strong><?php echo (!empty($arrInvoiceGeneralSetting['INCOME_MAINTENANCE_PARKING_UNIT_LABEL'])) ? $arrInvoiceGeneralSetting['INCOME_MAINTENANCE_PARKING_UNIT_LABEL'].':' : 'Parking Unit:' ?></strong></td>
                            <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;" ><?php echo (!empty($eachUnitDetail['arrMemberDetail']['parking_number']))? $eachUnitDetail['arrMemberDetail']['parking_number']:'';?></td>
                        </tr>
                                                        <?php } ?>
                        <tr>
                            <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;" ><strong><?php echo (!empty($arrInvoiceGeneralSetting['INCOME_MAINTENANCE_UNIT_NUMBER_LABEL'])) ? $arrInvoiceGeneralSetting['INCOME_MAINTENANCE_UNIT_NUMBER_LABEL'].':' : 'Unit Number:' ?></strong></td>
                            <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;" ><?php echo (!empty($eachUnitDetail['arrMemberDetail']['unit_flat_number']))? ucwords($eachUnitDetail['arrMemberDetail']['soc_building_name']).'/'.ucwords($eachUnitDetail['arrMemberDetail']['unit_flat_number']):'--';?></td>
                        </tr>
                        <?php if (empty($arrInvoiceGeneralSetting['INCOME_MAINTENANCE_HIDE_BUILTUP_AREA'])){?>
                        <tr>
                            <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;" ><strong><?php echo (!empty($arrInvoiceGeneralSetting['INCOME_MAINTENANCE_BUILTUP_AREA_LABEL'])) ? $arrInvoiceGeneralSetting['INCOME_MAINTENANCE_BUILTUP_AREA_LABEL'].':' : 'Builtup Area:' ?></strong></td>
                            <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;" ><?php echo (!empty($eachUnitDetail['arrMemberDetail']['unit_area']) && round($eachUnitDetail['arrMemberDetail']['unit_area'])!=0)? round($eachUnitDetail['arrMemberDetail']['unit_area']).' Sqft' : '--';?></td>
                        </tr>
                        <?php } ?>
                        <?php if (empty($arrInvoiceGeneralSetting['INCOME_MAINTENANCE_HIDE_GSTUIN'])){?>
                        <tr>
                            <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;" ><strong><?php echo (!empty($arrInvoiceGeneralSetting['INCOME_MAINTENANCE_GSTUIN_LABEL'])) ? $arrInvoiceGeneralSetting['INCOME_MAINTENANCE_GSTUIN_LABEL'].':' : 'GSTIN/UIN:' ?></strong></td>
                            <!-- <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;" ><?php echo (!empty($eachUnitDetail['arrMemberDetail']['gstin']))? $eachUnitDetail['arrMemberDetail']['gstin']:'';?></td> -->
                            <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;" ><?php if(!empty($eachInvoiceDetail['member_gstin']))
                                                                {
                                                                   echo ucwords($eachInvoiceDetail['member_gstin']);
                                                                }?></td>
                        </tr>
                        <?php } ?>
                    </tbody>
                </table>
            </div>
            <div style="width:50%;float:right;" >
                <table style="border-collapse:collapse;border-spacing:0;margin-bottom:10px;float:right;border-color:#646464;" >
                    <tbody>
                        <tr>
                            <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;" >&nbsp;</td>
                            <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;" >&nbsp;</td>
                        </tr>
                        <tr>
                            <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;" ><strong>Invoice No:</strong></td>
                            <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;" ># <?php echo strtoupper($eachInvoiceDetail['unit_invoice_number']);?> (<?php echo $invoice_type; ?>)</td>
                        </tr>
                        <tr>
                            <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;" ><strong>Invoice Date:</strong></td>
                            <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;" ><?php echo date('M d, Y', strtotime(str_replace('/', '-', $eachInvoiceDetail['invoice_date'])));?></td>
                        </tr>
                        <tr>
                            <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;" ><strong>Due Date:</strong></td>
                            <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;" ><?php echo (!empty($eachInvoiceDetail['due_date'])) ? date('M d, Y', strtotime(str_replace('/', '-', $eachInvoiceDetail['due_date']))) : 'None';?></td>
                        </tr>
                        <tr>
                            <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;" ><strong>Invoicing Period:</strong></td>
                            <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;" ><?php echo date('M d, Y', strtotime($eachInvoiceDetail['from_date']));?> To <?php echo date('M d, Y', strtotime($eachInvoiceDetail['to_date']));?></td>
                        </tr>
                        <?php if(!empty($eachUnitDetail['arrBuilderCreditDetail']['amount']) && $eachUnitDetail['arrBuilderCreditDetail']['amount']!=0){?>
                        <tr>
                            <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;" ><strong>Other Deposit:</strong></td>
                            <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;" ><?php echo 'Rs. '.number_format(round($eachUnitDetail['arrBuilderCreditDetail']['amount'],2),2,'.', '');?></td>
                        </tr>
                        <?php }?>
                                                        <?php // if(!empty(trim($arrTaxDetail[0]['tax_class_description']))){?>
<!--                                                        <tr>
                                <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;" ><strong>GST Number:</strong></td>
                                <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;" ><?php //echo $arrTaxDetail[0]['tax_class_description'];?> </td>
                        </tr>-->
                                                        <?php //}?>
                    </tbody>
                </table>
            </div>

            <div style="clear:both;" ></div>
            <div style="width:100%;" >
                <table style="border-collapse:collapse;border-spacing:0;width:100%;" >
                    <thead>
                        <tr>
<!--                <th style="border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:8px;vertical-align:top;background: #f4f4f4;">Sr No</th>-->
                            <th colspan = "<?php echo (!empty($eachUnitDetail['showBankWithoutTaxParitcular'])) ? $eachUnitDetail['showBankWithoutTaxParitcular'] : '';?>" style="border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:8px;vertical-align:top;background: #f4f4f4;">Particulars</th>
                                                                <?php if(!empty($arrInvoiceSetting['showRateSqft'])){ ?>
                            <th style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:8px;vertical-align:top;background: #f4f4f4;" >Rate</th>
                                                                <?php } ?>
                            <th style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:8px;vertical-align:top;background: #f4f4f4;" >Amount <span>(RS.)</span></th>
                                                                <?php if(count($eachUnitDetail['arrTaxDetail'])>0 && !empty($eachUnitDetail['arrAppliedTaxCategory']) && count($eachUnitDetail['arrAppliedTaxCategory'])>0){
                                                                    foreach($eachUnitDetail['arrAppliedTaxCategory'] as $eachTaxCategory) {?>
                            <th style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:8px;vertical-align:top;background: #f4f4f4;" ><?php echo ucwords($eachTaxCategory['tax_name']).' ('.$eachTaxCategory['tax_rate_type'].')';?></th>
                                                                <?php }}?>

                        </tr>
                    </thead>
                    <tbody>
                                                    <?php $i = 1;
                                                    foreach($eachInvoiceDetail['invoice_particulars'] as $key=>$eachInvoiceParticular)
                                                    { ?>
                        <tr>
<!--                <td style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.42857;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" ><?php echo $i;?></td>-->
                            <td colspan = "<?php echo (!empty($eachUnitDetail['showBankWithoutTaxParitcular'])) ? $eachUnitDetail['showBankWithoutTaxParitcular'] : '';?>" style="text-align:left !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.42857;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" ><?php echo $eachInvoiceParticular['particular_name'];//(strtolower(trim($eachInvoiceParticular['particular']))=='noc') ? 'Non-Occupancy Charges' : $eachInvoiceParticular['particular'];
                                                                $eachInvoiceParticular['particular'] = strtolower($eachInvoiceParticular['particular']);
//                                                                if(!empty($arrAppliedTaxDetail[str_replace(' ','',$eachInvoiceParticular['particular'])]['rate_type']))
//                                                                {
//                                                                    echo ' ('.$arrAppliedTaxDetail[str_replace(' ','',$eachInvoiceParticular['particular'])]['rate_type'].')';
//                                                                }
                                                                ?>
                            </td>
                                                                <?php if(!empty($arrInvoiceSetting['showRateSqft'])){ ?>
                            <td style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.42857;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" > <?php echo $eachInvoiceParticular['rule_charge'];?></td>
                                                                <?php } ?>
                            <td style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.42857;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" > <?php echo number_format(round($eachInvoiceParticular['amount'],2),2,'.', '');?></td>
                                                                <?php //echo '<pre>'.str_replace(' ','',$eachInvoiceParticular['particular']);print_r($arrTaxCategoryParticular); exit;
                                                                if(count($eachUnitDetail['arrTaxDetail'])>0 && !empty($eachUnitDetail['arrTaxCategoryParticular']) && count($eachUnitDetail['arrAppliedTaxCategory'])>0){
                                                                if(!empty($eachUnitDetail['arrTaxCategoryParticular'][str_replace(' ','',$eachInvoiceParticular['particular'])]))
                                                                {
                                                                    foreach($eachUnitDetail['arrTaxCategoryParticular'][str_replace(' ','',$eachInvoiceParticular['particular'])] as $eachTaxCategoryParticular) {
                                                                        ?>
                            <td style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.42857;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" > <?php echo $eachTaxCategoryParticular['tax_amount'];?></td>
                                                                        <?php }
                                                                }else{
                                                                    foreach($eachUnitDetail['arrAppliedTaxCategory'] as $eachAppliedTaxCategory) {?>
                            <td style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.42857;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" > 0.00</td>
                                                                <?php }}} ?>


                        </tr>
                                                    <?php $i++; } ?> 
                                                        <?php //if(!empty($lateChargeTotalTax) && $lateChargeTotalTax>0)
                                                        //if(!empty($eachUnitDetail['arrLateChargesDetail'])){ ?>
                        <tr>
<!--                <td style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.42857;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" ><?php //echo $i;?></td>-->
                            <td colspan = "<?php echo (!empty($eachUnitDetail['showBankWithoutTaxParitcular'])) ? $eachUnitDetail['showBankWithoutTaxParitcular'] : '';?>" style="text-align:left !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.42857;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" >Delayed Payment Charges (<?php echo number_format( $eachUnitDetail['arrLateChargesDetail']['simple_interest'],2,'.', '').$eachUnitDetail['arrLateChargesDetail']['interest_text']; ?>)
                                                                <?php echo (!empty($arrInvoiceSetting['showInterestBreakup']) && $eachUnitDetail['interestAmount']>0 && $eachInvoiceDetail['principal_amount']>0) ? '<br>(Previous Due Amount: '.number_format($eachInvoiceDetail['principal_amount'],2,'.', '').')' : '';?>                                                                
                            </td>
                                                                <?php if(!empty($arrInvoiceSetting['showRateSqft'])){ ?>
                            <td style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.42857;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" > <?php echo number_format( $eachUnitDetail['arrLateChargesDetail']['simple_interest'],2,'.', '');?></td>
                                                                <?php } ?>

                            <td style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.42857;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" > <?php echo number_format($eachUnitDetail['interestAmount'],2,'.', '');?></td>
                                                                <?php //echo '<pre>'.str_replace(' ','',$eachInvoiceParticular['particular']);print_r($arrTaxCategoryParticular); exit;
                                                                if(count($eachUnitDetail['arrTaxDetail'])>0 && !empty($eachUnitDetail['arrTaxCategoryParticular']) && count($eachUnitDetail['arrAppliedTaxCategory'])>0){
                                                                if(!empty($eachUnitDetail['arrTaxCategoryParticular']['latecharge']))
                                                                {
                                                                    foreach($eachUnitDetail['arrTaxCategoryParticular']['latecharge'] as $eachTaxCategoryParticular) {
                                                                        ?>
                            <td style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.42857;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" > <?php echo $eachTaxCategoryParticular['tax_amount'];?></td>
                                                                        <?php }
                                                                }else{
                                                                    foreach($eachUnitDetail['arrAppliedTaxCategory'] as $eachAppliedTaxCategory) {?>
                            <td style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.42857;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" > 0.00</td>
                                                                <?php }}} ?>
                        </tr>
              <?php //} 
                                                        if(!empty($eachInvoiceDetail['invoice_particulars']) && count($eachInvoiceDetail['invoice_particulars'])>0){?>

                        <tr>
                            <td colspan="<?php echo $eachUnitDetail['defaultColumnCount'];?>" style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.42857;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;font-weight: bold;" ><?php echo (count($eachUnitDetail['arrTaxDetail'])>0 && !empty($eachUnitDetail['arrTaxCategoryParticularSubtotal']) && count($eachUnitDetail['arrTaxCategoryParticularSubtotal'])>0) ? 'Sub Total:' : 'Invoice Total:';?></td>
                            <td style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.42857;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;font-weight: bold;" > <?php echo number_format(round($eachInvoiceDetail['invoice_amount_detail']['totalInvoiceAmount'] + $eachUnitDetail['interestAmount'],2),2,'.', '');?></td>
                                                            <?php if(count($eachUnitDetail['arrTaxDetail'])>0 && !empty($eachUnitDetail['arrTaxCategoryParticularSubtotal']) && count($eachUnitDetail['arrTaxCategoryParticularSubtotal'])>0){
                                                                foreach($eachUnitDetail['arrTaxCategoryParticularSubtotal'] as $eachTaxCategoryParticularSubtotal) {
                                                                        ?>
                            <td style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.42857;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;font-weight: bold;" > <?php echo number_format($eachTaxCategoryParticularSubtotal['tax_subtotal'],2,'.', '');?></td>
                                                                        <?php }}?>
                        </tr>
                                                        <?php } if($eachUnitDetail['taxColumnCount']>0) {?>
                        <tr>
                            <td colspan="<?php echo $eachUnitDetail['taxColumnCount']+$eachUnitDetail['defaultColumnCount'];?>" style="text-align: right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" ><strong>Invoice Total:</strong></td>
                            <td style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" > <strong><?php echo $eachUnitDetail['invoiceTotalAmount'];?></strong></td>
                        </tr>
                                                        <?php }?>
                        <tr>
                                                            <?php
                                                            $lastRow = 1;
                                                            if(!empty($arrInvoiceSetting['showBankDetail'])){
                                                                $eachUnitDetail['defaultColumnCount'] -= $arrInvoiceSetting['showBankDetail'];
                                                                $lastRow += 1; 
                                                                ?>
                            <td rowspan="4" colspan="<?php echo '';?>" style="text-align: left;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" ><strong>
                                <?php if(!empty($eachUnitDetail['arrMemberDetail']['vpa']) && !empty($arrInvoiceSetting['showVPADetail'])){?> 
                                    E-collect (VPA) Pay Via Netbanking:<br/><br/>
                                <?php } else { ?>
                                    CHEQUE / NEFT / IMPS Payment Instruction:<br/><br/>
                                <?php } ?>
                                
                                                                <?php echo ($arrInvoiceSetting['arrBankDetail']['account_name']) ? 'Account Name : '.$arrInvoiceSetting['arrBankDetail']['account_name'].'<br/>':'';
                                                                if(!empty($eachUnitDetail['arrMemberDetail']['vpa']) && !empty($arrInvoiceSetting['showVPADetail']))
                                                                {
                                                                    echo 'Account Number : '.$eachUnitDetail['arrMemberDetail']['vpa'].'<br/>';
                                                                    echo 'Account Type : Current<br/>';
                                                                    echo 'IFSC :'.DISPLAY_YES_VPA;
                                                                }
                                                                else
                                                                {
                                                                    echo ($arrInvoiceSetting['arrBankDetail']['account_number']) ? 'Account Number : '.$arrInvoiceSetting['arrBankDetail']['account_number'].'<br/>':'';
                                                                     echo ($arrInvoiceSetting['arrBankDetail']['bank_name']) ? 'Bank Name : '.$arrInvoiceSetting['arrBankDetail']['bank_name'].'<br/>':'';
                                                                     echo ($arrInvoiceSetting['arrBankDetail']['branch']) ? 'Branch : '.$arrInvoiceSetting['arrBankDetail']['branch'].'<br/>':'';
                                                                     echo ($arrInvoiceSetting['arrBankDetail']['bank_ifsc']) ? 'IFSC : '.$arrInvoiceSetting['arrBankDetail']['bank_ifsc']:'';
                                                                }
                                                                
                                                                
                                                                ?>
                                </strong></td>
                                                            <?php }?>

                            <td colspan="<?php echo $eachUnitDetail['taxColumnCount']+$eachUnitDetail['defaultColumnCount'];?>" style="text-align: right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" >Credit/Adjustment:</td>
                            <td style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" ><?php echo '(-) '.number_format($eachUnitDetail['advanceAmount'],2,'.', '');?></td>
                        </tr>
                                                        <?php //if(!empty($arrAppliedTaxCategory) && count($arrAppliedTaxCategory)>0){ ?>
                        <tr>
                            <td colspan="<?php echo $eachUnitDetail['taxColumnCount']+$eachUnitDetail['defaultColumnCount'];?>" style="text-align: right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" ><strong>Grand Total<?php echo ($eachInvoiceDetail['roundoff_amount'] == 0) ? '' : ' (Rounded By '.$eachInvoiceDetail['roundoff_amount'].')';?>:</strong></td>
                            <td style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" > <strong><?php echo $eachUnitDetail['grandTotalAmount'];?></strong></td>
                        </tr>
                                                        <?php //} ?>

                                                        <?php if(empty($eachUnitDetail['lateChargeTotalTax']) || $eachUnitDetail['lateChargeTotalTax']<=0){ ?>
<!--                                                        <tr>
        <td colspan="<?php //echo $eachUnitDetail['taxColumnCount']+$eachUnitDetail['defaultColumnCount'];?>" style="text-align: right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" >Delayed Payment Charges (<?php echo number_format( $arrLateChargesDetail['simple_interest'],2,'.', '').$arrLateChargesDetail['interest_text']; ?>):
                                                                    <?php echo (!empty($arrInvoiceSetting['showInterestBreakup']) && $eachUnitDetail['interestAmount']>0 && $eachInvoiceDetail['principal_amount']>0) ? '<br>(Previous Due Amount: '.number_format($eachInvoiceDetail['principal_amount'],2,'.', '').')' : '';?></td>
        <td style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" > <?php echo '(+) '.number_format($eachUnitDetail['interestAmount'],2,'.', '');?>
        </td>
</tr>-->
<!--<tr>
        <td colspan="<?php //echo $eachUnitDetail['taxColumnCount']+$eachUnitDetail['defaultColumnCount'];?>" style="text-align: right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" >Tax On Delayed Payment Charges<?php //echo (!empty($arrLateChargeTaxClass['latecharge']['rate_type'])) ? ' ('.ucwords(rtrim($arrLateChargeTaxClass['latecharge']['rate_type'],', ')).')' : '';?>:</td>
        <td style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" > (+) <?php //echo number_format($lateChargeTotalTax,2,'.', '');?></td>
</tr>-->
                                                        <?php } ?>

                        <tr>
                            <td colspan="<?php echo $eachUnitDetail['taxColumnCount']+$eachUnitDetail['defaultColumnCount'];?>" style="text-align: right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" >Principal Arrears: </td>
                            <td style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" ><?php echo '(+) '.number_format($eachUnitDetail['outstandingPrincipalAmount'],2,'.', '');?></td>
                        </tr>
                        <tr>
                            <td colspan="<?php echo $eachUnitDetail['taxColumnCount']+$eachUnitDetail['defaultColumnCount'];?>" style="text-align: right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" >Interest Arrears:</td>
                            <td style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" ><?php echo '(+) '.number_format($eachUnitDetail['outstandingInterestAmount'],2,'.', '');?></td>
                        </tr>
<!--                                                        <tr>
                            <td colspan="<?php echo $eachUnitDetail['taxColumnCount']+$eachUnitDetail['defaultColumnCount'];?>" style="text-align: right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" >Credit/Adjustment:</td>
                            <td style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" ><?php echo '(-) '.number_format($eachUnitDetail['advanceAmount'],2,'.', '');?></td>
                        </tr>-->

                        <tr style="font-size: <?php echo $arrInvoiceSetting['invoiceFontSize']; ?>px;">
                            <td colspan="<?php echo $eachUnitDetail['taxColumnCount']+$eachUnitDetail['defaultColumnCount']+$arrInvoiceSetting['showBankDetail'];?>" style="text-align: right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" ><strong>Balance Due :  </strong></td>
                            <td style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;font-size: <?php echo $arrInvoiceSetting['invoiceFontSize']; ?>px;" ><strong> <?php echo number_format($eachUnitDetail['balanceDue'],2,'.', '');?> </strong></td>
                        </tr>
                        <tr style="font-size: <?php echo $arrInvoiceSetting['invoiceFontSize']; ?>px;">
                            <td colspan="<?php echo $eachUnitDetail['taxColumnCount']+$eachUnitDetail['defaultColumnCount']+$lastRow;?>" style="text-align: right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:4px;padding-left:8px;vertical-align:top;" ><strong>Amount In Words:</strong> <span><?php echo $eachUnitDetail['rupeesInWord'];?> </span></td>
                        </tr>
                    </tbody>
                </table>
                <table style="border-collapse:collapse;border-spacing:0;width:100%;" >

                </table>
                <div>
                    <!-- <?php// if(!empty($arrCreditNoteText)){?>
                        <?php //echo $arrCreditNoteText;  ?>
                    <?php// } ?> -->
                    <?php if(sizeof($arrCreditNoteText)>0){ ?>
                        <p style="line-height:21px; margin-top: 0px; margin-bottom: 0px;" >
                            <strong>Credit Note Adjustment:</strong>
                        </p>
                        <table style="border-collapse:collapse;border-spacing:0;width:100%;" >
                            <thead>
                                <tr>
                                     <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:8px;vertical-align:top;background: #f4f4f4;">Sr No</th>
                                    <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:8px;vertical-align:top;background: #f4f4f4;">Rectification Date</th>
                                    <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:8px;vertical-align:top;background: #f4f4f4;" >Rectified Invoice N0#</th>
                                    <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:8px;vertical-align:top;background: #f4f4f4;" >Rectified Particular </th>
                                    <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:8px;vertical-align:top;background: #f4f4f4;" >Rectified Amount <span>(RS.)</span></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $i = 1;  foreach($arrCreditNoteText as $cn){ ?>
                                    <tr>
                                    <td style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:8px;vertical-align:top;" ><?php echo $i; ?></td>
                                    <td style="border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" > 
                                        <?php
                                         echo ($cn['payment_date'])?date("d/m/Y", strtotime($cn['payment_date'])):""; 
                                        ?></td>
                                    <td style="border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" ><?php echo $cn['rectified_invoice_number']; ?> </td>
                                    <td style="border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" ><?php echo $cn['rectified_particular_name']; ?> </td>
                                    <td style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" > <?php echo $cn['amount']; ?></td>
                                </tr>
                                <?php $i ++;} ?>
                            </tbody>
                        </table>
                    <?php }?>
                </div>
            </div>
        <?php if(!empty($arrInvoiceSetting['arrInvoiceGeneralNote'])){?>

            <div style="width: 100%;float: left;padding-top:5px; font-size:<?php echo $arrInvoiceSetting['invoiceFontSize']; ?>px;">
                <p style="line-height:12px; margin-top:0px; margin-bottom: 0px;">
                    <strong>Note:<br>
            <?php foreach($arrInvoiceSetting['arrInvoiceGeneralNote'] as $eachRow){?>
                                                    <?php echo ucfirst($eachRow);?><br>
                                                <?php } ?>
                    </strong>
                </p>
            </div>
                                <?php }
                                //if(!empty($arrTaxDetail[0]['tax_categories_footer'])){?>
            <!--        <div style="clear:both;" ></div>
                                            <div style="width: 100%;float: left;">
                                                    <p style="line-height: 12px; margin-top: 0px; margin-bottom: 0px;" >
                                                            <strong>Tax Detail:</strong>
                                                            <ul style="line-height: 0.5;">
                                                                    <li><?php // foreach($arrTaxDetail as $key=>$eachTaxDetail)
//                                                            {
//                                                                echo ucfirst($eachTaxDetail['tax_categories_footer']).'<br><br>';

                                                           // } ?>
                                                                    </li>
                                                            </ul>
                                                    </p>
                                            </div>-->
        <?php //}?>
            <div style="clear:both;" ></div>

        <?php } }
        else
        {
            echo 'No active rule to generate Invoice.';
        }?> 

        <?php if($eachUnitDetail['latePaymentamount']) { ?>
            <p style="line-height:21px; margin-top: 0px; margin-bottom: 0px;" >
                <strong>Late Payment Calculations:</strong>
            </p>
            <table style="border-collapse:collapse;border-spacing:0;width:100%;" >
                <thead>
                    <tr>
                        <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:8px;vertical-align:top;background: #f4f4f4;">Sr No</th>
                        <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:8px;vertical-align:top;background: #f4f4f4;">Invoice Number</th>
                        <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:8px;vertical-align:top;background: #f4f4f4;" >Invoice Date</th>
                        <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:8px;vertical-align:top;background: #f4f4f4;" >Particular</th>
                        <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:8px;vertical-align:top;background: #f4f4f4;" >Amount</th>
                        <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:8px;vertical-align:top;background: #f4f4f4;" >Interest Applied For</th>
                        <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:8px;vertical-align:top;background: #f4f4f4;" >Interest Rate </th>
                        <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:8px;vertical-align:top;background: #f4f4f4;" >Delayed Days </th>
                        <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:8px;vertical-align:top;background: #f4f4f4;" >Interest Posted  </th>
                    </tr>
                </thead>
                <tbody>
                                        <?php $i = 1; 
                                        foreach($eachUnitDetail['arrLatePaymentStack'] as $LatePayment) {
                                                if(!empty($LatePayment) && $LatePayment['interest'] != 0) {
                                            ?>
                    <tr>
                        <td style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:8px;vertical-align:top;" ><?php echo $i; ?> </td>
                        <td style="border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" > <?php echo $LatePayment['invoice_number']; ?> </td>
                        <td style="border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" ><?php echo date('M d, Y', strtotime(str_replace('/', '-',$LatePayment['invoice_date']))); ?> </td>
                        <td style="border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" ><?php echo ($LatePayment['display_name'] != '') ? $LatePayment['display_name'] : $LatePayment['particular']; ?> </td>
                        <td style="border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;text-align:right !important;" > <?php echo $LatePayment['amount']; ?> </td>
                        <td style="border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;text-align:right !important;" ><?php echo $LatePayment['unpaid_amount']; ?> </td>
                        <td style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" > <?php echo $LatePayment['rate']; ?> </td>
                        <td style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" > <?php echo $LatePayment['delay_days']; ?> </td>
                        <td style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" > <?php echo $LatePayment['interest']; ?> </td>
                    </tr>
                                            <?php $i++; }} ?>
                    <tr>
                        <td style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" colspan="6">
                                                    <?php echo $eachUnitDetail['intrestPrinciple']; ?>
                        </td>
                        <td style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" colspan="3">
                                                    <?php echo $eachUnitDetail['latePaymentamount']; ?>
                        </td>
                    </tr>
                    
                </tbody>
            </table>
        <?php } ?>
        <?php if(!empty($eachUnitDetail['arrLastPeriodPaymentTransaction'])){ ?>
            <div style="width:100%;" >
                <p style="line-height:21px; margin-top: 0px; margin-bottom: 0px;" >
                    <strong>Payment Receipt:</strong>
                    <br><span>Details of payment received from <?php echo date('M d, Y', strtotime(str_replace('/', '-', $eachUnitDetail['arrLastPeriodPaymentTransaction']['invoice_period_detail']['from_date']))). ' to '.date('M d, Y', strtotime(str_replace('/', '-', $eachUnitDetail['arrLastPeriodPaymentTransaction']['invoice_period_detail']['to_date']))) ; ?>.</span>
                </p>
                <table style="border-collapse:collapse;border-spacing:0;width:100%;" >
                    <thead>
                        <tr>
                            <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:8px;vertical-align:top;background: #f4f4f4;">Sr No</th>
                            <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:8px;vertical-align:top;background: #f4f4f4;">Payment Date</th>
                            <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:8px;vertical-align:top;background: #f4f4f4;" >Receipt N0#</th>
                            <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:8px;vertical-align:top;background: #f4f4f4;" >Received From</th>
                            <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:8px;vertical-align:top;background: #f4f4f4;" >Mode</th>
                            <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:8px;vertical-align:top;background: #f4f4f4;" >Drawee Bank</th>
                            <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:8px;vertical-align:top;background: #f4f4f4;" >Receipt Note</th>
                            <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:8px;vertical-align:top;background: #f4f4f4;" >Amount <span>(RS.)</span></th>
                        </tr>
                    </thead>
                    <tbody>
        <?php $i = 1; 
        foreach($eachUnitDetail['arrLastPeriodPaymentTransaction']['payment_transaction_detail'] as $eachPaymentTransaction){ 
            if(strtolower($eachPaymentTransaction['receipt_from']) != 'incidental') {
            ?>
                        <tr>
                            <td style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:4px;padding-bottom:4px;padding-right:4px;padding-left:8px;vertical-align:top;" ><?php echo $i; ?></td>
                            <td style="border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" > <?php echo date('M d, Y', strtotime(str_replace('/', '-', $eachPaymentTransaction['payment_date']))); ?></td>
                            <td style="border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" ><?php echo $eachPaymentTransaction['receipt_number']; ?> </td>
                            <td style="border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" ><?php echo $eachPaymentTransaction['received_from']; ?> </td>
                            <td style="border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" > 
                                <?php
                                echo $eachPaymentTransaction['payment_mode'];
                                ?>
                                </td>
                            <td style="border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" ><?php echo $eachPaymentTransaction['payment_reference']; ?> </td>
                            <td style="border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" > <?php echo $eachPaymentTransaction['payment_note']; ?></td>
                            <td style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" > <?php echo $eachPaymentTransaction['total_amount']; ?></td>
                        </tr>
        <?php $i++; } 
            } ?>

                        <tr style="font-size: <?php echo $arrInvoiceSetting['invoiceFontSize']; ?>px;">
                            <td colspan="7" style="text-align: left !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:4px;padding-left:8px;vertical-align:top;" ><strong>Amount In Words:</strong> <span><?php echo $eachUnitDetail['arrLastPeriodPaymentTransaction']['rupeesInWords']; ?> </span></td>
                            <td style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding-top:2px;padding-bottom:2px;padding-right:4px;padding-left:8px;vertical-align:top;" > <strong><?php echo $eachUnitDetail['arrLastPeriodPaymentTransaction']['total']; ?></strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        <?php } ?>
        <?php if(!empty($arrInvoiceSetting['showSocietySignature']) && !empty(trim($arrSocietyDetail['soc_name']))){?>
            <br/><br/>
            <div style="width: 100%;">
                <div style="line-height:21px; margin-top:0px; margin-bottom: 0px; text-align: right;">For <?php echo ($arrSocietyDetail['soc_name']);?>
                    <br></br>
                    <?php if (empty($arrInvoiceGeneralSetting['INCOME_MAINTENANCE_SIGNATURE_LABEL'])) { ?>
                        <span style="margin: 0 15px;text-align: right;">Authorised Signatory _______________________________</span>
                    <?php } else { ?>
                        <span style="margin: 0 15px;text-align: right;"><?php echo $arrInvoiceGeneralSetting['INCOME_MAINTENANCE_SIGNATURE_LABEL'];?></span>
                    <?php } ?>
                </div>
            </div>
        <?php }?>

        </div>

        <div class="invoice-footer" style="position: fixed; bottom: 50px;">                                
        <?php if(!empty($arrInvoiceSetting['showChsoneFooter'])){?>
            <div style="border-bottom-width: 1px;
                 height: 1px;
                 width: 100%;
                 background: #000;"></div>

            <div style="width: 100%; font-size: <?php echo $arrInvoiceSetting['invoiceFontSize']; ?>px;">
                <div style="line-height:21px; margin-top:0px; margin-bottom: 0px; text-align: center;">Your society is powered by OneSociety - "Digital Community"</div>
                <div style="line-height:21px; margin-top:0px; margin-bottom: 0px; text-align: center;">Website: www.cubeoneapp.com | Email: <EMAIL> | Phone: 022-20870052</div>
            </div>
        <?php }?>                        
            <div style="width: 100%;">
                <p style="line-height:21px; margin-top:0px; margin-bottom: 0px;">E. & O.E</p>
            </div>  
        </div>                  
    </div>
</div>
        <?php }
        $totalInvoiceCount++;
        echo '<div style="page-break-after:always;"></div>';
        //echo ($totalInvoiceCount != $arrInvoiceSetting['totalInvoiceCount']) ? '<div style="page-break-after:always;"></div>' : '';
        } } }
else
{
    echo 'No active rule to generate Invoice.';
}?>
