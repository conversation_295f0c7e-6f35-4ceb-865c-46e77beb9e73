<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trial Balance</title>
    <style>
        body {
            font-family: Arial, sans-serif;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .table-container {
            width: 100%;
            margin: 0 auto;
            border-collapse: collapse;
        }
        .table-container th, .table-container td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
        }
        .table-container th {
            background-color: #f2f2f2;
        }
        .report-footer {
            margin-top: 20px;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>NATRAJ CO-OPERATIVE HOUSING SOCIETY LTD.</h2>
        <p>[REGN.NO:NBOM/CIDCO/HSG(OH)/588/JTR/1998 DATED 07/01/1998]</p>
        <p>Plot No-35, Sector-11, Kharghar, Navi Mumbai, Maharashtra-410210</p>
        <h3>Trial Balance for 2024-2025</h3>
    </div>

    <table class="table-container">
        <thead>
            <tr>
                <th>Particular</th>
                <th>Opening (Debit)</th>
                <th>Closing (Debit)</th>
                <th></th>
                <th>Particular</th>
                <th>Opening (Credit)</th>
                <th>Closing (Credit)</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($data as $row)
                <tr>
                    <td>{{ $row['debit_particular'] ?? '-' }}</td>
                    <td>{{ number_format($row['opening_debit'] ?? 0, 2) }}</td>
                    <td>{{ number_format($row['closing_debit'] ?? 0, 2) }}</td>
                    <td></td>
                    <td>{{ $row['credit_particular'] ?? '-' }}</td>
                    <td>{{ number_format($row['opening_credit1'] ?? 0, 2) }}</td>
                    <td>{{ number_format($row['closing_credit1'] ?? 0, 2) }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>

    <div class="report-footer">
        <p>Report Date: {{ $reportDate ?? now()->format('d-m-Y') }}</p>
    </div>
</body>
</html>
