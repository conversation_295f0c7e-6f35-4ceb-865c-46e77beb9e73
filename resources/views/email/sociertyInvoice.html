<html>
    <head>
        <title>Invoice</title>
        <style>

            .container{
                width: 100%;
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
                border: 1px solid #ccc;
            }
            .header {
                margin-top: 3%;
                text-align: center;
            }
            
            .header h1 {
                font-size: 18px;
                font-weight: 600;
                margin: 0;
                margin-bottom: 10px;
            }

            .header p {
                font-size: 11px;
                margin: 0;
                font-weight: 600;
                line-height: 1.5;
            }

            .to-details {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-top: 2%;
            }

            .to-details .left {
                width: 65%;
            }

            .to-details .right {
                width: 35%;
            }

            .to-details p {
                font-size: 11px;
                margin: 0;
                line-height: 1.5;
            }

            .to-details p span {
                font-weight: 600;
            }

            .to-details .left p span:first-child {
                width: 16%;
                display: inline-block;
            }

            .to-details .right p span:first-child {
                width: 35%;
                display: inline-block;
            }

            .table {
                margin-top: 2%;
            }

            .table table {
                width: 100%;
                border-collapse: collapse;
            }

            .table table thead th {
                font-size: 11px;
                padding: 10px;
                border: 1px solid #3a3a3a;
                background-color: #9a9a9a;
            }

            .table table tr td {
                font-size: 11px;
                padding: 10px;
                border: 1px solid #3a3a3a;
                text-align: right;
            }

            .table table tr td span {
                font-weight: 600;
            }

            .table table tr td p {
                font-weight: 600;
                margin: 0;
            }

            .table table tr td div{
                margin-top: 15px;
            }

            .table table tr td div p {
                margin: 3px auto;
            }

            .table table thead th:first-child, .table table tbody td:first-child {
                text-align: left;
            }

            .table table tr td:first-child {
                width: 40%;
            }

            .table table tr td right {
                float: right;
            }

            .notes {
                font-size: 11px;
                font-weight: 600;
            }

            .notes p{
                margin: 2px auto;
            }

            .notes ul {
                margin: 0;
                padding: 0;
                list-style: none;
                list-style-type: none; /* Remove the default bullet points */
                counter-reset: list-counter;
            }

            ul li::before {
                counter-increment: list-counter; /* Increment the counter for each list item */
                content: counter(list-counter)"."; /* Display the current counter value before the list item */
                margin-right: 5px; /* Add some spacing between the number and the list item text */
                font-weight: bold; /* Optionally, make the numbers bold */
            }

            .signature {
                margin-top: 3%;
                text-align: right;
                font-size: 12px;
            }

            .sign span{
                margin-top: 30px;
                width: 30%;
                display: inline-block;
                height: 1px;
                background-color: #3a3a3a;
            }
        </style>
    </head>
    <body>

        <div class="container">

            <div class="header">
                <h1>Cyber One Premises Co-Operative Society Ltd</h1>
                <p>Reg No: N.B.O.M/CIDCO/GENERAL(C)/1702/JTR/YEAR 2017-2018 | GSTIN/UIN: 27AAEAC2416M1ZR</p>
                <p>Cyber One, Plot-4 & 6, Sector-30a, Vashi, Near Odissa Bhavan, Navi Mumbai, Maharashtra, 400703</p>
                <p>Email: <EMAIL> | Phone: 7507387540 </p>
            </div>

            <div class="to-details">
                <div class="left">
                    <p><span>To,</span></p>
                    <p><span>Name:</span><span>Greenscape Developers Pvt Ltd</span></p>
                    <p><span>Parking Unit:</span>211,212</p>
                    <p><span>Unit Number:</span>Cyber One/1905</p>
                    <p><span>Builtup Area:</span>1195 Sqft</p>
                    <p><span>GSTIN/UIN:</span>27AACCG4829L1ZA</p>
                </div>
                <div class="right">
                    <p></p>
                    <p>
                        <span>Invoice No:</span>
                        # INV00148 (Original)
                    </p>
                    <p>
                        <span>Invoice Date:</span>
                        Oct 01, 2018
                    </p>
                    <p>
                        <span>Due Date:</span>
                        Oct 31, 2018
                    </p>
                    <p>
                        <span>Invoicing Period:</span>
                        Oct 01, 2018 To Mar 31, 2019
                    </p>
                </div>
            </div>

            <div class="table">
                <table>
                    <thead>
                        <th>Particulars</th>
                        <th>Rates</th>
                        <th>HSN/SAC Code</th>
                        <th>Amount (RS.)</th>
                        <th>CGST (9%)</th>
                        <th>SGST (9%)</th>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Maintenance Fee</td>
                            <td>4.80</td>
                            <td>9995</td>
                            <td>57360.00</td>
                            <td>5162.40</td>
                            <td>5162.40</td>
                        </tr>
                        <tr>
                            <td>Delayed Payment Charges (0.00)</td>
                            <td></td>
                            <td></td>
                            <td>0.00</td>
                            <td>0.00</td>
                            <td>0.00</td>
                        </tr>
                        <tr>
                            <td colspan="3"><span><right>Sub Total:</right></span></td>
                            <td><span>57360.00</span></td>
                            <td><span>5162.40</span></td>
                            <td><span>5162.40</span></td>
                        </tr>
                        <tr>
                            <td colspan="5"><span><right>Invoice Total:</right></span></td>
                            <td><span>67684.80</span></td>
                        </tr>

                        <tr>
                            <td rowspan="5">
                                <p>E-collect (VPA) Pay Via Netbanking:</p>
                                <div>
                                    <p>Account Name : CYBER ONE PREMISES CO OPERATIVE SOCIETY LTD.</p>
                                    <p>Account Number : CHSONECYBEROCO1905</p>
                                    <p>Account Type : Current</p>
                                    <p>IFSC :YESB0CMSNOC</p>
                                </div>
                            </td>
                            <td colspan="4"><right>Credit/Adjustment:</right></td>
                            <td>(-) 0.00</td>
                        </tr>

                        <tr>
                            <td colspan="4"><span><right>Grand Total:</right></span></td>
                            <td><span>67684.80</span></td>
                        </tr>

                        <tr>
                            <td colspan="4"><right>Principal Arrears:</right></td>
                            <td>(+) 312612.00</td>
                        </tr>
                        <tr>
                            <td colspan="4"><right>Interest Arrears:</right></td>
                            <td>(+) 0.00</td>
                        </tr>
                        <tr>
                            <td colspan="4"><span><right>Balance Due :</right></span></td>
                            <td><span>380296.80</span></td>
                        </tr>
                        <tr>
                            <td colspan="6">
                                <right><span>Amount In Words:</span> Three Lakh Eighty Thousand Two Hundred Ninety Six Rupees Eighty Paise</right>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="notes">
                <p>Note:</p>
                <ul>
                    <li>Pay your maintenance (CAM) bill before due date to avoid late payment charges. </li>
                    <li>21% Interest will be charged on total CAM amount if not paid on or before the due date. </li>
                    <li>Use convenient payment options to pay your dues through online transfer or cheque. </li>
                    <li>To pay download ONEAPP mobile app or visit www.cubeone.com</li>
                    <li>Kindly make cheque in the favour of "CYBER ONE PREMISES CO OPERATIVE SOCIETY LTD"</li>
                </ul>
                <p>Bank Name : IDBI Bank</p>
                <p>A/c Number : ****************</p> 
                <p>IFSC Code : IBKL0000123 </p>
                <p>Branch : Palm Beach Road, Vashi. </p>
            </div>

            <div class="signature">
                <p>For Cyber One Premises Co-Operative Society Ltd</p>
                <p class="sign">Authorised Signatory<span> </span></p>
            </div>

        </div>

    </body>
</html>