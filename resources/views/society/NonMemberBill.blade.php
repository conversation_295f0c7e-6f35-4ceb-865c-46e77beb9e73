<!DOCTYPE html>
<html lang="en">
   <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Invoice</title>
      <style>
         body {
         font-size: 14px;
         margin: 0;
         padding: 0;
         }
         table {
         width: 100%;
         border-collapse: collapse;
         }
         table, th, td {
         border: 1px solid #646464; /* Apply borders to table, th, and td */
         }
         th, td {
         padding: 8px; /* Add padding for readability */
         }
         th {
         background-color: #f4f4f4; /* Light background for headers */
         }
      </style>
   </head>
   <body>
      <div style="width:100%;margin:0;padding:0;font-size:14px;">
         <div style="margin:0;padding:0;">
            <div style="width:100%;text-align:center;margin-top:2px;">
               <h2 style="margin-bottom: 0;margin-top: 0px;">{{$data['arrSocietyDetail']['soc_name']}}</h2>
               <div style="width:100%;text-align:center;line-height:1.5;color:#000000;margin-top:5px;margin-bottom:15px;margin-right:0px;margin-left:0px;font-size: 11px;">
                  Reg No: {{$data['arrSocietyDetail']['soc_reg_num']}}
                  @isset($data['arrSocietyDetail']['soc_gst_number'])
                  | GSTIN/UIN: {{$data['arrSocietyDetail']['soc_gst_number']}}
                  @endisset
                  <br/>
                  {{$data['arrSocietyDetail']['soc_address_1']}}, {{$data['arrSocietyDetail']['soc_address_2']}}, {{$data['arrSocietyDetail']['soc_landmark']}}, {{$data['arrSocietyDetail']['soc_city_or_town']}}, {{$data['arrSocietyDetail']['soc_state']}}, {{$data['arrSocietyDetail']['soc_pincode']}}.<br>
                  @isset($data['arrSocietyDetail']['soc_office_email'])
                  Email: {{$data['arrSocietyDetail']['soc_office_email']}}
                  @endisset
                  @isset($data['arrSocietyDetail']['soc_office_mobile'])
                  | Phone: {{$data['arrSocietyDetail']['soc_office_mobile']}}
                  @endisset
               </div>
               @isset($data['arrSocietyDetail']['year'])
               {{$data['arrSocietyDetail']['year']}}
               @endisset
            </div>
            <div style="clear:both;"></div>
            <div style="width:50%;float:left;margin-top:0;">
               <table style="border: none; border-collapse: collapse; width: 100%;">
                  <tbody>
                     <tr>
                        <td style="border: none;"><strong>To,</strong></td>
                     </tr>
                     <tr>
                        <td style="border: none;"><strong>Name:</strong></td>
                        <td style="border: none;"><strong>{{ $data['billed_name'] ?? '' }}</strong></td>
                     </tr>
                     <tr>
                        <td style="border: none;"><strong>GSTIN/UIN:</strong></td>
                        <td style="border: none;">{{ $data['gstin'] ?? '' }}</td>
                     </tr>
                     <tr>
                        <td style="border: none;"><strong>HSN/SAC</strong></td>
                        <td style="border: none;">{{ $data['hsn'] ?? '' }}</td>
                     </tr>
                     <tr>
                        <td style="border: none;"><strong>Address:</strong></td>
                        <td style="border: none;">{{ $data['booker_email_address'] ?? '' }}</td>
                     </tr>
                     <tr>
                        <td style="border: none;"><strong>Mobile Number:</strong></td>
                        <td style="border: none;">{{ $data['booker_mobile_number'] ?? '' }}</td>
                     </tr>
                  </tbody>
               </table>
            </div>
            <div style="width:50%;float:right;">
               <table style="border: none; border-collapse: collapse; width: 100%;">
                  <tbody>
                     <tr>
                        <td style="border: none;"><strong>Invoice No:</strong></td>
                        <td style="border: none;"># {{ $data['bill_number'] ?? '' }}</td>
                     </tr>
                     <tr>
                        <td style="border: none;"><strong>Invoice Date:</strong></td>
                        <td style="border: none;">{{ $data['bill_date'] ?? '' }}</td>
                     </tr>
                     <tr>
                        <td style="border: none;"><strong>Invoicing Period:</strong></td>
                        <td style="border: none;">{{ $data['from_date'] ?? '' }} to {{ $data['end_date'] ?? '' }}</td>
                     </tr>
                  </tbody>
               </table>
            </div>
            <div style="clear:both;"></div>
            <div style="width:100%;">
               <table>
                  <thead>
                     <tr>
                        <th colspan="6">Particulars</th>
                        <th>Amount (RS.)</th>
                     </tr>
                  </thead>
                  <tbody>
                     <tr>
                        <td colspan="6">{{$data['bill_for']}}</td>
                        <td style="text-align:right;">{{$data['bill_amount']}}</td>
                     </tr>
                     <tr>
                        <td rowspan="4" colspan="5">
                           <strong>CHEQUE / NEFT / IMPS Payment Instruction:</strong><br>
                           <p>Account Number: {{ $data['arrBankDetail']['account_number'] ?? '' }}</p>
                           <p>Bank Name: {{ $data['arrBankDetail']['bank_name'] ?? '' }}</p>
                           <p>Branch: {{ $data['arrBankDetail']['branch'] ?? '' }}</p>
                           <p>IFSC: {{ $data['arrBankDetail']['bank_ifsc'] ?? '' }}</p>
                        </td>
                        <td><strong>Invoice Amount:</strong></td>
                        <td style="text-align:right;">{{$data['bill_amount']}}</td>
                     </tr>
                     <tr>
                        <td>Advance Amount:</td>
                        <td style="text-align:right;">(-) {{$data['advance_amount']}}</td>
                     </tr>
                     <tr>
                        <td><strong>Balance Due:</strong></td>
                        <td style="text-align:right;"><strong>{{$data['balance_due']}}</strong></td>
                     </tr>
                     <tr>
                        <td colspan="2"><strong>Amount In Words:</strong> {{$data['total_due_in_words']}}</td>
                     </tr>
                  </tbody>
               </table>
            </div>
            <div style="width:100%;padding-top:5px;font-size:13px;">
               <p style="line-height:12px;margin-top:0;margin-bottom:0;">
                  <strong>Note:</strong><br>
                  @foreach ($data['arrInvoiceGeneralNote'] ?? [] as $item)
                  @if (!empty($item)) <!-- Skip empty lines -->
                  {!! nl2br(e($item)) !!}<br>
                  @endif
                  @endforeach
               </p>
            </div>
            <div style="clear:both;"></div>
            <div style="clear:both;" ></div>
            <div style="width: 100%;">
               <div style="line-height:21px; margin-top:0px; margin-bottom: 0px; text-align: right;">
                  For Futurescape Technologies Pvt Ltd<br><br>
                  <span style="margin: 0 15px;">Cubeoneapp</span>
               </div>
            </div>
         </div>
      </div>
   </body>
</html>