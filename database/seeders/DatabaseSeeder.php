<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // \App\Models\User::factory(10)->create();

        // \App\Models\User::factory()->create([
        //     'name' => 'Test User',
        //     'email' => '<EMAIL>',
        // ]);

        // add data to the tasks and jobs tables
        \App\Models\TaskModel::factory(10)->create(
            [
                'soc_id' => 412,
                'title' => 'Task 1',
                'description' => 'Task 1 description',
                'status' => 0,
                'created_by' => 1,
            ]
        );
        \App\Models\JobModel::factory(10)->create(
            [
                'task_id' => 1,
                'user_id' => 1,
                'email_or_phone' => '<EMAIL>',
                'type' => 'email',
                'subject' => 'Test email',
                'content' => 'This is a test email',
                'url' => 'https://example.com',
                'cc_users' => '<EMAIL>, <EMAIL>',
                'bcc_users' => '',
                'status' => 0,
            ]
        );
    }
}
