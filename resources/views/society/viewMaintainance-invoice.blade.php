<!DOCTYPE html>
<!-- saved from url=(0095)https://society.cubeonebiz.com/admin/income-details/downloadInvoice/1/INV2021_00419/viewInvoice -->
<html class="no-js" lang="en">
   <head>
      <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>OneSociety</title>
      <link rel="shortcut icon" href="https://society.cubeonebiz.com/images/favicon.png">
      <style>
         html {
         font-size: 62.5%;
         -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
         margin: 20px;
         }
         body {
         font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
         margin: 20px;
         background: #ffffff;
         }
         .printOption {
         clear: both;
         width: 100%;
         background: #001941;
         height: 40px;
         }
         .printOption a {
         float: right;
         margin-top: 5px;
         }
         .printOption .fa-print {
         font-size: 2.5rem;
         }
         .printOption h3 {
         text-align: center;
         position: relative;
         left: 50%;
         float: left;
         margin: 0 0 0 -70px;
         }
         ul.printOpt li {
         float: left;
         padding: 0 10px;
         display: inline-block;
         }
         p.society {
         float: left;
         padding: 5px 10px;
         color: #fff;
         font-size: 14px;
         margin: 5px 0;
         }
         .right {
         float: right !important;
         }
         .invoice-main {
         padding: 20px;
         }
         .invoice-footer {
         position: fixed;
         bottom: 20px;
         width: calc(100% - 40px);
         text-align: center;
         margin: 0 auto;
         }
         .preview-watermark {
         position: fixed;
         bottom: 120px; /* Adjust this to ensure it appears above the footer */
         width: 100%;
         text-align: center;
         font-size: 3rem; /* Adjust size for better visibility */
         color: rgba(0, 0, 0, 0.1); /* Light and subtle text color */
         text-transform: uppercase;
         font-weight: bold;
         letter-spacing: 2px;
         pointer-events: none; /* Prevent interaction */
         user-select: none; /* Prevent text selection */
         z-index: 1; /* Ensure it's visible and above other content */
         }
         .invoice-footer {
         z-index: 2; /* Ensure the footer remains above other content */
         }
      </style>
   </head>
   <body>
      <div style="width:100%;margin-top:0px;margin-bottom:0px;margin-right:0px;margin-left:0px;padding-top:0px;padding-bottom:0px;padding-right:0px;padding-left:0px;font-size:13px; ">
         <div style="margin-top:0;margin-bottom:0;margin-right:10px;margin-left:10px;padding-left:0px;padding-right:0px;">
            <div class="invoice-main" style="padding-bottom: 50px;">
               <div style="width:100%;text-align:center;margin-top:2px;">
                  <h2 style="margin-bottom: 0;margin-top: 0px;">
                     {{ $data['arrSocietyDetail']['soc_name'] ?? '' }}
                  </h2>
                  <div style="width:100%;text-align:center;line-height:1.5;color:#000000;margin-top:5px;margin-bottom:15px;font-size:13px;">
                     <strong>
                     {{ $data['arrSocietyDetail']['soc_address_1'] ?? '' }},
                     {{ $data['arrSocietyDetail']['soc_address_2'] ?? '' }},
                     {{ $data['arrSocietyDetail']['soc_landmark'] ?? '' }},
                     {{ $data['arrSocietyDetail']['soc_city_or_town'] ?? '' }},
                     {{ $data['arrSocietyDetail']['soc_state'] ?? '' }}
                     </strong>
                  </div>
                  <div style="width:100%;text-align:center;line-height:1.5;color:#000000;margin-top:2px;margin-bottom:15px;font-size:11px;">
                     <strong>
                         @if(!empty($data['arrSocietyDetail']['soc_office_email']))
                             Email: {{ $data['arrSocietyDetail']['soc_office_email'] }}
                         @endif
                 
                         @if(!empty($data['arrSocietyDetail']['soc_office_email']) && !empty($data['arrSocietyDetail']['soc_office_mobile']))
                             |
                         @endif
                 
                         @if(!empty($data['arrSocietyDetail']['soc_office_mobile']))
                             Phone: {{ $data['arrSocietyDetail']['soc_office_mobile'] }}
                         @endif
                     </strong>
                 </div>
               </div>
               <div style="clear:both;"></div>
               {{-- Check for errors in any unit detail --}}
                     @php
                     $firstError = collect($data['arrAllUnitInvoiceDetail'] ?? [])
                        ->first(fn($detail) => !empty($detail['error']))['message'] ?? null;
                     @endphp

                     @if($firstError)
                     <div class="alert alert-danger">
                        {!! $firstError !!}
                     </div>

                     {{-- Stop here; do not render invoice --}}
                     @php return; @endphp
                     @endif

               {{-- NO ERROR → continue rendering the full invoice --}}
               <div style="width:50%;float:left; margin-top:0px;">
                  <table style="border-collapse:collapse;border-spacing:0;margin-bottom:10px;float:left;border-color:#646464;">
                     <tbody>
                        <tr>
                           <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;"><strong>To,</strong></td>
                        </tr>
                        <tr>
                           <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;"><strong>Name:</strong></td>
                           <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;"><strong>{{ $data['creditNoteAdjustment']['bill_to'] ?? '' }} {{ $data['arrMemberDetail']['member_last_name'] ?? '' }}</strong></td>
                        </tr>
                        <tr>
                           <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;"><strong>Parking Unit:</strong></td>
                           <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;">{{ $data['creditNoteAdjustment']['parking_unit'] ?? '' }}</td>
                        </tr>
                        <tr>
                           <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;"><strong>Unit Number:</strong></td>
                           <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;">{{ $data['creditNoteAdjustment']['soc_building_name'] ?? '' }} / {{ $data['creditNoteAdjustment']['unit_name'] ?? '' }}</td>
                        </tr>
                        <tr>
                           <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;"><strong>Builtup Area:</strong></td>
                           <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;">{{ $data['arrMemberDetail']['unit_area'] ?? $data['creditNoteAdjustment']['unit_area'] ?? '' }} Sqft</td>
                        </tr>
                        <tr>
                           <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;"><strong>GSTIN/UIN:</strong></td>
                           <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;">{{ $data['arrMemberDetail']['gstin'] ?? '' }}</td>
                        </tr>
                     </tbody>
                  </table>
               </div>
               <div style="width:50%;float:right;">
                  <table style="border-collapse:collapse;border-spacing:0;margin-bottom:10px;float:right;border-color:#646464;">
                     <tbody>
                        <tr>
                           <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;">&nbsp;</td>
                           <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;">&nbsp;</td>
                        </tr>
                        <tr>
                           <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;"><strong>Invoice No:</strong></td>
                           <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;"># {{ $data['creditNoteAdjustment']['invoice_number'] ?? '' }} (Originial)</td>
                        </tr>
                        <tr>
                           <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;"><strong>Invoice Date:</strong></td>
                           <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;">{{ $data['creditNoteAdjustment']['from_date'] ?? '' }}</td>
                        </tr>
                        <tr>
                           <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;"><strong>Due Date:</strong></td>
                           <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;">{{ $data['creditNoteAdjustment']['due_date'] ?? '' }}</td>
                        </tr>
                        <tr>
                           <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;"><strong>Invoicing Period:</strong></td>
                           <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;">{{ $data['creditNoteAdjustment']['from_date'] ?? '' }} To {{ $data['creditNoteAdjustment']['to_date'] ?? '' }}</td>
                        </tr>
                     </tbody>
                  </table>
               </div>
               <div style="clear:both;"></div>
               <div style="width:100%;">
                  <table style="border-collapse:collapse;border-spacing:0;width:100%;">
                     <thead>
                        <tr>
                           <th style="border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;background:#f4f4f4;">Particulars</th>
                           <th style="border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;background:#f4f4f4;">HSN/SAC Code</th>
                           <th style="text-align:right;border-width:1px;border-style:solid;border-color:#646464;line-height:1;padding:4px;background:#f4f4f4;">Amount <span>(RS.)</span></th>
                        </tr>
                     </thead>
                     <tbody>
                        @foreach ($data['invoice_particular'] ?? [] as $particular)
                        <tr>
                           <td style="text-align:left;border-width:1px;border-style:solid;border-color:#646464;line-height:1.42857;padding:3px 8px;vertical-align:top;">{!! $particular['particular'] ?? '' !!}</td>
                           <td style="text-align:left;border-width:1px;border-style:solid;border-color:#646464;line-height:1.42857;padding:3px 8px;vertical-align:top;">{!! $particular['hsn_sac_code'] ?? '' !!}</td>
                           <td style="text-align:right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.42857;padding:3px 8px;vertical-align:top;">{{ $particular['amount'] ?? '' }}</td>
                        </tr>
                        @endforeach
                        <tr>
                           <!--                <td style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.42857;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;" ></td>-->
                                                       <td colspan="2" style="text-align:left !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.42857;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;">Delayed Payment Charges (21.00% per year)
                                                                                           <br>(Previous Due Amount: <?php echo $data['creditNoteAdjustment']['principal_amount']; ?>)</td>                                                                
                                                       </td>
                                                                                           
                                                       <td style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.42857;padding-top:2px;padding-bottom:2px;padding-right:3px;padding-left:8px;vertical-align:top;"> <?php echo $data['creditNoteAdjustment']['interest_amount'];?></td>
                                                                                                                   </tr>
                        <tr>
                           <td colspan="2" style="text-align:right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.42857;padding:3px 8px;vertical-align:top;font-weight:bold;">Invoice Total:</td>
                           <td style="text-align:right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.42857;padding:3px 8px;vertical-align:top;font-weight:bold;">{{ $data['total_array']['total_amount'] ?? '' }}</td>
                        </tr>
                        <tr>
                           <td rowspan="4" colspan="" style="text-align:left;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:3px 8px;vertical-align:top;">
                              <strong>
                              CHEQUE / NEFT / IMPS Payment Instruction:<br><br>
                              Account Name : {{ $data['arrBankDetail']['account_name'] ?? '' }}<br>
                              Account Number : {{ $data['arrBankDetail']['account_number'] ?? '' }}<br>
                              Bank Name : {{ $data['arrBankDetail']['bank_name'] ?? '' }}<br>
                              Branch Name : {{ $data['arrBankDetail']['branch'] ?? '' }}<br>
                              IFSC : {{ $data['arrBankDetail']['bank_ifsc'] ?? '' }}
                              </strong>
                           </td>
                           <td colspan="1" style="text-align:right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:3px 8px;vertical-align:top;">Credit/Adjustment:</td>
                           <td style="text-align:right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:3px 8px;vertical-align:top;">(-) {{ $data['creditNoteAdjustment']['advance_amount'] ?? '' }}</td>
                        </tr>
                        <tr>
                           <td colspan="1" style="text-align:right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:3px 8px;vertical-align:top;"><strong>Grand Total</strong></td>
                           <td style="text-align:right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:3px 8px;vertical-align:top;"><strong>{{ $data['total_array']['total_amount'] ?? '' }}</strong></td>
                        </tr>
                        <tr>
                           <td colspan="1" style="text-align:right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:3px 8px;vertical-align:top;">Principal Arrears:</td>
                           <td style="text-align:right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:3px 8px;vertical-align:top;">(+) {{ $data['creditNoteAdjustment']['outstanding_principal'] ?? '' }}</td>
                        </tr>
                        <tr>
                           <td colspan="1" style="text-align:right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:3px 8px;vertical-align:top;">Interest Arrears:</td>
                           <td style="text-align:right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:3px 8px;vertical-align:top;">(+) {{ $data['creditNoteAdjustment']['outstanding_interest'] ?? '' }}</td>
                        </tr>
                        <tr style="font-size:13px;">
                           <td colspan="2" style="text-align:right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:3px 8px;vertical-align:top;"><strong>Balance Due:</strong></td>
                           <td style="text-align:right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:3px 8px;vertical-align:top;font-size:13px;"><strong>{{ $data['total_array']['grand_total'] ?? '' }}</strong></td>
                        </tr>
                        <tr style="font-size:13px;">
                           <td colspan="3" style="text-align:right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:3px 8px;vertical-align:top;"><strong>Amount In Words:</strong> <span>{{ $data['total_array']['grand_total_in_words'] ?? '' }}</span></td>
                        </tr>
                     </tbody>
                  </table>
                  <table style="border-collapse:collapse;border-spacing:0;width:100%;">
                  </table>
                  <div>
                  </div>
               </div>
               <div style="width: 100%;float: left;padding-top:5px; font-size:13px;">
                  <p style="line-height:12px; margin-top:0px; margin-bottom: 0px;">
                     <strong>
                        Note:
                        <br>
                        @foreach ($data['arrInvoiceGeneralNote'] ?? [] as $item)
                        @if (!empty($item)) <!-- Skip empty lines -->
                        {!! nl2br(e($item)) !!}<br>
                        @endif
                        @endforeach
                     </strong>
                  </p>
               </div>
               <div style="clear:both;"></div>
            </div>
      @if (!empty($data['Last_payment_receipt_detail']['payment_transaction_detail']))
    <div style="width:100%;">
        <p style="line-height:21px; margin-top: 0px; margin-bottom: 0px;">
            <strong>Payment Receipt:</strong>
            <br>
            <span>
                Details of payment received from 
                {{ \Carbon\Carbon::createFromFormat('Y-m-d', str_replace('/', '-', $data['Last_payment_receipt_detail']['invoice_period_detail']['from_date']))->format('M d, Y') }} 
                to 
                {{ \Carbon\Carbon::createFromFormat('Y-m-d', str_replace('/', '-', $data['Last_payment_receipt_detail']['invoice_period_detail']['to_date']))->format('M d, Y') }}.
            </span>
        </p>
        <table style="border-collapse:collapse;border-spacing:0;width:100%;">
            <thead>
                <tr>
                    <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;background: #f4f4f4;">Sr No</th>
                    <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;background: #f4f4f4;">Payment Date</th>
                    <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;background: #f4f4f4;">Receipt No#</th>
                    <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;background: #f4f4f4;">Received From</th>
                    <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;background: #f4f4f4;">Mode</th>
                    <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;background: #f4f4f4;">Drawee Bank</th>
                    <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;background: #f4f4f4;">Receipt Note</th>
                    <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;background: #f4f4f4;">Amount <span>(RS.)</span></th>
                </tr>
            </thead>
            <tbody>
                @php $i = 1; @endphp
                @foreach ($data['Last_payment_receipt_detail']['payment_transaction_detail'] as $eachPaymentTransaction)
                   
                <tr>
                        <td style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;">{{ $i }}</td>
                        <td style="border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;">
                        </td>
                        <td style="border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;">{{ $eachPaymentTransaction['receipt_number'] }}</td>
                        <td style="border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;">{{ $eachPaymentTransaction['received_from'] }}</td>
                        <td style="border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;">{{ $eachPaymentTransaction['payment_mode'] }}</td>
                        <td style="border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;">{{ $eachPaymentTransaction['payment_reference'] }}</td>
                        <td style="border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;">{{ $eachPaymentTransaction['payment_note'] }}</td>
                        <td style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;">{{ $eachPaymentTransaction['total_amount'] }}</td>
                    </tr>
                    @php $i++; @endphp
                @endforeach

                <tr>
                    <td colspan="7" style="text-align:left !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;">
                        <strong>Amount In Words:</strong> <span>{{ $data['Last_payment_receipt_detail']['rupeesInWords'] }}</span>
                    </td>
                    <td style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;">
                        <strong>{{ $data['Last_payment_receipt_detail']['total'] }}</strong>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    
@endif
            <div class="preview-watermark">Preview</div>
            <div class="invoice-footer" style="position: fixed; bottom: 50px; width: 100%; text-align: center;">
               <div style="border-bottom-width: 1px; height: 1px; width: 100%; background: #000; margin: 0 auto;"></div>
               <div style="width: 100%; font-size: 13px; text-align: center;">
                  <div style="line-height:21px; margin-top:0px; margin-bottom: 0px; text-align: center;">Your society is powered by OneSociety - "Digital Community"</div>
                  <div style="line-height:21px; margin-top:0px; margin-bottom: 0px; text-align: center;">Website: www.cubeoneapp.com | Email: <EMAIL> | Phone: 022-20870052</div>
               </div>
               <div style="width: 100%; text-align: center;">
                  <p style="line-height:21px; margin-top:0px; margin-bottom: 0px;">E. &amp; O.E</p>
               </div>
            </div>
         </div>
      </div>
      </div>
      
<div style="width:100%;text-align:right">
   <span>For {{ $data['arrSocietyDetail']['soc_name'] ?? '' }}</span>
</div>

      <script type="text/javascript">
         function PrintDiv(id) {
            var data = document.getElementById(id).innerHTML;
            var myWindow = window.open('', '', 'height=400,width=600');
            myWindow.document.write('<html><head><title></title>');
            /*optional stylesheet*/ //myWindow.document.write('<link rel="stylesheet" href="main.css" type="text/css" />');
            myWindow.document.write('</head><body >');
            myWindow.document.write(data);
            myWindow.document.write('</body></html>');
            myWindow.document.close(); // necessary for IE >= 10
            //return false;
            myWindow.onload = function() { // necessary if the div contain images
               myWindow.focus(); // necessary for IE >= 10
               myWindow.print();
               myWindow.close();
            };
         }
      </script>
      <script src="chrome-extension://hhojmcideegachlhfgfdhailpfhgknjm/web_accessible_resources/index.js"></script>
   </body>
</html>