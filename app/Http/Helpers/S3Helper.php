<?php

namespace App\Http\Helpers;

use Aws\S3\S3Client;
use Aws\S3\Exception\S3Exception;

class S3Helper {

    public static function uploadFileToS3($file, $request){
        // Create an S3 client using config instead of env
        $s3 = new S3Client([
            'version' => 'latest',
            'region' => config('filesystems.disks.s3.region'),
            'credentials' => [
                'key' => config('filesystems.disks.s3.key'),
                'secret' => config('filesystems.disks.s3.secret'),
            ],
        ]);

        // Set the bucket name and file name
        $bucketName = config('filesystems.disks.s3.bucket');
        $fileName = $request['service_id'] . '/' . $request['company_id'] . '/' . $request['user_id'] . '/' .  $request['file_name'] . '.' . $request['file_type'];

        try {
            // Upload the file to the S3 bucket
            $response = $s3->putObject([
                'Bucket' => $bucketName,
                'Key' => $fileName,
                'Body' => $file->output(),
            ]);

            $filepath = $response->get('ObjectURL');

            return response()->json([
                'data' => $filepath,
                'message' => 'success',
                'status' => 200
            ]);
        } catch (S3Exception $e) {
            return response()->json([
                'message' => 'There was an error uploading the file.',
                'error' => $e->getMessage(),
                'status' => 500
            ]);
        }
    }

    public static function uploadExcelFileToS3($file, $request){
        // Create an S3 client using config instead of env
        $s3 = new S3Client([
            'version' => 'latest',
            'region' => config('filesystems.disks.s3.region'),
            'credentials' => [
                'key' => config('filesystems.disks.s3.key'),
                'secret' => config('filesystems.disks.s3.secret'),
            ],
        ]);

        // Set the bucket name and file name
        $bucketName = config('filesystems.disks.s3.bucket');
        $fileName = $request['service_id'] . '/' . $request['company_id'] . '/' . $request['user_id'] . '/' .  $request['file_name'] . '.' . $request['file_type'];

        // get the file from the path and upload it to s3
        $filePath = $file->getPathname();

        try {
            // Upload the file to the S3 bucket
            $response = $s3->putObject([
                'Bucket' => $bucketName,
                'Key' => $fileName,
                'Body' => fopen($filePath, 'r')
            ]);

            $filepath = $response->get('ObjectURL');

            return response()->json([
                'data' => $filepath,
                'message' => 'success',
                'status' => 200
            ]);
        } catch (S3Exception $e) {
            return response()->json([
                'message' => 'There was an error uploading the file.',
                'error' => $e->getMessage(),
                'status' => 500
            ]);
        }
    }

    public static function uploadToS3($request) {

        ini_set('upload_max_filesize', '8M');

        $file = $request->file('file');
        $path = $request->path;
        $company_id = $request['company_id'];

        // Create an S3 client using config instead of env
        $s3 = new S3Client([
            'version' => 'latest',
            'region' => config('filesystems.disks.s3.region'),
            'credentials' => [
                'key' => config('filesystems.disks.s3.key'),
                'secret' => config('filesystems.disks.s3.secret'),
            ],
        ]);

        // Set the bucket name and file name
        $bucketName = config('filesystems.disks.s3.bucket');
        $fileOriginalName = time() . '_' . $file->getClientOriginalName();
        // $fileName = $request['service_id'] . '/' . $request['company_id'] . '/' . $request['uuid'] . '/' .  $path . '/' . $fileOriginalName;
        $fileName = $company_id . '/' .  $path . '/' . $fileOriginalName;
        $fileName = str_replace(' ', '_', $fileName);

        try {
            // Upload the file to the S3 bucket
            $s3->putObject([
                'Bucket' => $bucketName,
                'Key' => $fileName,
                'Body' => fopen($file, 'r'),
                // 'ACL' => 'public-read', // make the file public
            ]);

            $filepath = "https://$bucketName.s3.amazonaws.com/$fileName";

            return response()->json([
                'data' => $filepath,
                'message' => 'success',
                'status' => 200
            ]);
        } catch (S3Exception $e) {
            return response()->json([
                'message' => 'There was an error uploading the file.',
                'error' => $e->getMessage(),
                'status' => 500
            ]);
        }
    }
}