<?php

namespace App\Http\Helpers;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use Maatwebsite\Excel\Events\AfterSheet;

class JsonExport implements FromArray, WithHeadings, WithStyles, WithEvents, WithCustomStartCell
{
    protected $data;
    protected $headings;
    protected $hasReportDetails;

    public function __construct(array $data, array $headings)
    {
        $this->data = $data;
        $this->headings = $headings;
        $this->hasReportDetails = !empty($headings['report_heading']['report_name']) || !empty($headings['report_heading']['report_date_range']);
    }

    public function array(): array
    {
        return $this->data;
    }

    public function headings(): array
    {
        // Extract the report heading details
        $reportHeading = $this->headings['report_heading'] ?? [];
        $soc_name = $reportHeading['soc_name'] ?? '';
        $soc_reg_num = $reportHeading['soc_reg_num'] ?? '';
        $soc_add = $reportHeading['soc_add'] ?? '';
        $report_name = $reportHeading['report_name'] ?? '';
        $report_date_range = $reportHeading['report_date_range'] ?? '';

        // Combine report details into a single cell with new lines
        $society_details = implode("\n", array_filter([$soc_name, $soc_reg_num, $soc_add]));
        $report_details = implode("\n", array_filter([$report_name, $report_date_range]));

        // Get the table headings
        $tableHeadings = $this->headings['table_heading'] ?? [];
        $tableHeadings = preg_replace("#_#", " ", $tableHeadings);
        $tableHeadings = array_map("ucwords", $tableHeadings);

        // Create new headers array
        $count = count($tableHeadings);
        $colspan = array_fill(0, $count - 1, '');

        if ($this->hasReportDetails) {
            return [
                array_merge([$society_details], $colspan),
                array_merge([$report_details], array_fill(0, $count - 2, '')), // New row for report details
                $tableHeadings
            ];
        } else {
            return [
                array_merge([$society_details], $colspan),
                $tableHeadings
            ];
        }
    }

    public function styles(Worksheet $sheet)
    {
        // Define styles for the heading cells
        $start = $this->hasReportDetails ? '3' : '2';
        $sheet->getStyle('A1:' . $sheet->getHighestColumn() . $start)->applyFromArray([
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'DDDDDD'],
            ],
            'font' => [
                'bold' => true,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
            'alignment' => [
                'wrapText' => true,
                'horizontal' => 'center',
                'vertical' => 'center',
            ],
        ]);

        // Apply number format to all columns to prevent scientific notation
        foreach (range('A', $sheet->getHighestColumn()) as $column) {
            $sheet->getStyle($column)->getNumberFormat()->setFormatCode('0'); // '0' format ensures the number is displayed fully
        }
    }

    public function startCell(): string
    {
        return 'A1';
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $sheet = $event->sheet->getDelegate();
                $tableHeadings = $this->headings['table_heading'] ?? [];
                $count = count($tableHeadings);

                // Merge cells for the society details
                $sheet->mergeCells("A1:" . $this->columnLetter($count) . "1");

                // Merge cells for the report details
                if ($this->hasReportDetails) {
                    $sheet->mergeCells("A2:" . $this->columnLetter($count) . "2");
                }

                // Set row heights to accommodate multiple lines
                $sheet->getRowDimension(1)->setRowHeight(60);
                if ($this->hasReportDetails) {
                    $sheet->getRowDimension(2)->setRowHeight(40);
                }

                // Autosize columns
                foreach (range('A', $this->columnLetter($count)) as $column) {
                    $sheet->getColumnDimension($column)->setAutoSize(true);
                }
            },
        ];
    }

    private function columnLetter($columnNumber)
    {
        $columnLetter = '';
        while ($columnNumber > 0) {
            $columnNumber--;
            $columnLetter = chr(65 + $columnNumber % 26) . $columnLetter;
            $columnNumber = (int)($columnNumber / 26);
        }
        return $columnLetter;
    }
}
