<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

use App\Models\JobModel;
use App\Models\TaskModel;

class SendBulkEmailSMS extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:send-bulk-email-sms';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send bulk email and SMS to selected entries in jobs table.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $jobs = JobModel::where('status', 0)->get();
        // get first 2 jobs
        // $jobs = JobModel::where('status', 0)->limit(2)->get();
        
        foreach ($jobs as $job) {

            if ($job->type === 'email') {
                // Send email
                $this->sendEmail($job);

            } else {
                // Send SMS
                $this->sendSMS($job);
            }
        }
    }

    private function sendEmail($job)
    {
        $email = $job->email_or_phone;
        $subject = $job->subject;
        $view = 'empty';
        $content = $job->content;
        $attachments = $job->url;
        $attachments = is_array($attachments) ? $attachments : [$attachments];
        $cc_users = explode(',', $job->cc_users);
        if($job->bcc_users == null || $job->bcc_users == ''){
            $bcc_users = [];
        }
        else{
            $bcc_users = explode(',', $job->bcc_users);
        }

        $mail = Mail::to($email)->cc($cc_users);
        
        if (!empty($bcc_users)) {
            $mail->bcc($bcc_users);
        }
        
        $mail->send(new \App\Mail\SendMail($subject, $view, $attachments, $content));
        
        if ($mail) {
            $job->status = 1;
            $job->save();
        } else {
            // $job->status = 0;
            $job->is_failed = 1;
            $job->failed_reason = 'Failed to send email';
            $job->save();
        }
    }

    private function sendSMS($job)
    {
        $field = $job->email_or_phone;

        // if field does not contain country code, add it
        if (strlen($field) === 10) {
            $field = '91' . $field;
        }

        $otp = "123456";

        $text = "<#> oneapp: ". $otp ." is your one time password (OTP) to activate your account. aayFRZqxWgl";
        $balance = $this->checkSMSBalance();
        if($balance->original['body'] > 0){
            $url = env('24X7SMS_BASEURL').'/SendSMS.aspX?EmailID='.env('24X7SMS_EMAIL').'&Password='.env('24X7SMS_PASSWORD').'&ServiceName='.env('24X7SMS_SERVICENAME').'&MobileNo='.$field.'&Message='.urlencode($text).'&SenderID='.env('24X7SMS_SENDERID').'&GSMID=1';

            $result = $this->getRequest($url);

            $job->status = 1;
            $job->save();
        }else{

            $job->is_failed = 1;
            $job->failed_reason = 'Insufficient balance';
            $job->save();
        }
    }

    private function checkSMSBalance()
    {
        $url = env('24X7SMS_BASEURL').'/BalanceCheck.aspX?EmailID='.env('24X7SMS_EMAIL').'&Password='.env('24X7SMS_PASSWORD').'&ServiceName='.env('24X7SMS_SERVICENAME');

        $result = $this->getRequest($url);

        return response()->json(['status'=>200, 'message'=>"Balance retrieved successfully", 'body'=>$result]);
    }

    private function getRequest($url)
    {
        $curl = curl_init();

        curl_setopt_array($curl, array(
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
        ));

        $response = curl_exec($curl);

        curl_close($curl);
        $result = json_encode($response, true);

        return $response;
    }
}
