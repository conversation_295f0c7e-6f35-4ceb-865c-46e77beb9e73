<html>
    <head>
        <title>Invoice</title>
        <style>

            .container{
                /* width: 100%;
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
                border: 1px solid #ccc; */
            }
            .header {
                margin-top: 3%;
                text-align: center;
            }

            .header img {
                margin: 0 auto 20px auto;
                display: block;
            }
            
            .header h1 {
                font-size: 18px;
                font-weight: 600;
                margin: 0;
                margin-bottom: 10px;
            }

            .header p {
                font-size: 11px;
                margin: 0;
                font-weight: 600;
                line-height: 1.5;
            }

            .to-details {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-top: 2%;
            }

            .to-details .left {
                width: 65%;
            }

            .to-details .right {
                width: 35%;
            }

            .to-details p {
                font-size: 11px;
                margin: 0;
                line-height: 1.5;
            }

            .to-details p span {
                font-weight: 600;
            }

            .to-details .left p span:first-child {
                width: 16%;
                display: inline-block;
            }

            .to-details .right p span:first-child {
                width: 35%;
                display: inline-block;
            }

            .table {
                margin-top: 2%;
            }

            .table table {
                width: 100%;
                border-collapse: collapse;
            }

            .table table thead th {
                font-size: 11px;
                padding: 10px;
                border: 1px solid #3a3a3a;
                background-color: #ddd;
            }

            .table table tr td {
                font-size: 11px;
                padding: 10px;
                border: 1px solid #3a3a3a;
                text-align: right;
            }

            .table table tr td span {
                font-weight: 600;
            }

            .table table tr td p {
                font-weight: 600;
                margin: 0;
            }

            .table table tr td div{
                margin-top: 15px;
            }

            .table table tr td div p {
                margin: 3px auto;
            }

            .table table thead th:first-child, .table table tbody td:first-child {
                text-align: left;
            }

            /* .table table tr td:first-child {
                width: 40%;
            }

            .table table tr td:last-child {
                width: 20%;
            } */

            .table table th right, .table table tr td right {
                float: right;
            }

            .notes {
                font-size: 11px;
                font-weight: 600;
            }

            .notes p{
                margin: 2px auto;
            }

            .notes ul {
                margin: 0;
                padding: 0;
                list-style: none;
                list-style-type: none; /* Remove the default bullet points */
                counter-reset: list-counter;
            }

            ul li::before {
                counter-increment: list-counter; /* Increment the counter for each list item */
                content: counter(list-counter)"."; /* Display the current counter value before the list item */
                margin-right: 5px; /* Add some spacing between the number and the list item text */
                font-weight: bold; /* Optionally, make the numbers bold */
            }

            .signature {
                margin-top: 3%;
                text-align: right;
                font-size: 12px;
            }

            .sign span{
                margin-top: 30px;
                width: 30%;
                display: inline-block;
                height: 1px;
                background-color: #3a3a3a;
            }

            .ticket-card{
                margin-top: 20px;
                display: inline-block;
                padding: 20px 15px 5px 15px;
                border: 1px dashed #3a3a3a;
                background-color: #efeeee;
                border-bottom-left-radius: 20px;
                border-top-right-radius: 20px;
            }
        </style>
    </head>
    <body>

        <div class="container">

            <div class="header">
                <img src="https://society.cubeonebiz.com/images/one-society.png" alt="logo" width="100px">
                <h1>{{$company_details['soc_name']}}</h1>
                <p>Reg No: {{$company_details['soc_reg_num']}}</p>
                <p>{{$company_details['soc_address_1']}}, {{$company_details['soc_address_2']}}, {{$company_details['soc_city_or_town']}}, {{$company_details['soc_state']}}, {{$company_details['soc_pincode']}}.</p>
            </div>

            <div class="to-details">
                <div class="left">
                    <p><span>To,</span></p>
                    <p><span>Name:</span><span>{{$data['member_first_name']}} {{$data['member_last_name']}}</span></p>
                    <p><span>Unit Number:</span>{{$data['unit_flat_number']}}</p>
                    <p><span>GSTIN/UIN:</span>{{$data['member_gstin']}}</p>
                </div>
                <div class="right">
                    <p></p>
                    <p>
                        <span>Invoice No:</span>
                        # {{$data['invoice_number']}} (Original)
                    </p>
                    <p>
                        <span>Invoice Date:</span>
                        {{date('d M Y', strtotime($data['bill_date']))}}
                    </p>
                    <p>
                        <span>Due Date:</span>
                        {{date('d M Y', strtotime($data['due_date']))}}
                    </p>
                    <p>
                        <span>Invoicing Period:</span>
                        <!-- {{$data['from_date']}} - {{$data['to_date']}} -->
                    </p>
                </div>
            </div>

            <div class="table">
                <table>
                    <thead>
                        <th colspan="2">Particulars</th>
                        <th><right>Amount (RS.)</right></th>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="2">{{$data['particular']}}</td>
                            <td>{{$data['amount']}}</td>
                        </tr>
                        <tr>
                            <td colspan="2">Delayed Payment Charges @if($data['interest_amount']) (on amount {{$data['principal_amount']}} ) @else (0.00) @endif</td>
                            <td>@if($data['interest_amount']) {{round($data['interest_amount'], 2)}} @else 0.00 @endif</td>
                        </tr>
                        <tr>
                            <td rowspan="7">
                                <p>CHEQUE / NEFT / IMPS Payment Instruction:</p>
                                <div>
                                    <p>Account Name : {{$data['company_bank_accounts']['account_name']}}</p>
                                    <p>Account Number : {{$data['company_bank_accounts']['account_number']}}</p>
                                    <p>Account Type : Current</p>
                                    <p>IFSC : {{$data['company_bank_accounts']['bank_ifsc']}}</p>

                                    <span class="ticket-card">Please Pay Rs. {{$data['total_due'] }}/-</span>

                                </div>
                            </td>
                            <td><span><right>Invoice Total:</right></span></td>
                            <td><span>{{$data['amount']}}</span></td>
                        </tr>

                        <tr>
                            <td><right>Advance Amount:</right></td>
                            <td>(-) {{$data['advance_amount']}}</td>
                        </tr>

                        <tr>
                            <td><span><right>Grand Total @if($data['roundoff_amount']) ( Rounded {{ round($data['roundoff_amount'], 2) }} ) @endif</right></span></td>
                            <td><span>{{$data['grand_total']}}</span></td>
                        </tr>

                        <tr>
                            <td><right>Principal Arrears:</right></td>
                            <td>(+) {{$data['outstanding_principal']}}</td>
                        </tr>
                        <tr>
                            <td><right>Interest Arrears:</right></td>
                            <td>(+) {{$data['outstanding_interest']}}</td>
                        </tr>

                        <tr>
                            <td><span><right>Balance Due:</right></span></td>
                            <td><span>{{$data['total_due']}}</span></td>
                        </tr>
                        <tr>
                            <td colspan="2">
                                <right><span>Amount In Words:</span> {{$data['total_due_in_words']}}</right>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="notes">
                <p>Note: <span>Before {{date('d M Y', strtotime($data['due_date']))}}</span></p>
            </div>

            <div class="signature">
                <p>For {{$company_details['soc_name']}}</p>
                <p class="sign">Authorised Signatory<span> </span></p>
            </div>

        </div>

    </body>
</html>