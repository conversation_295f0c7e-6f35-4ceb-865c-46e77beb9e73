<!DOCTYPE html>
<!-- saved from url=(0095)https://society.cubeonebiz.com/admin/income-details/downloadInvoice/1/INV2021_00419/viewInvoice -->
<html class="no-js" lang="en">
   <head>
      <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>OneSociety</title>
      <link rel="shortcut icon" href="https://society.cubeonebiz.com/images/favicon.png">
      <style>
         html {
         font-size: 62.5%;
         -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
         margin: 20px;
         }
         body {
         font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
         margin: 20px;
         background: #ffffff;
         }
         .printOption {
         clear: both;
         width: 100%;
         background: #001941;
         height: 40px;
         }
         .printOption a {
         float: right;
         margin-top: 5px;
         }
         .printOption .fa-print {
         font-size: 2.5rem;
         }
         .printOption h3 {
         text-align: center;
         position: relative;
         left: 50%;
         float: left;
         margin: 0 0 0 -70px;
         }
         ul.printOpt li {
         float: left;
         padding: 0 10px;
         display: inline-block;
         }
         p.society {
         float: left;
         padding: 5px 10px;
         color: #fff;
         font-size: 14px;
         margin: 5px 0;
         }
         .right {
         float: right !important;
         }
         .invoice-main {
         padding: 20px;
         page-break-after: always;
         }
         .invoice-main:last-child {
         page-break-after: avoid;
         }
         .invoice-footer {
         position: fixed;
         bottom: 20px;
         width: calc(100% - 40px);
         text-align: center;
         margin: 0 auto;
         }
         .preview-watermark {
         position: fixed;
         bottom: 120px; /* Adjust this to ensure it appears above the footer */
         width: 100%;
         text-align: center;
         font-size: 3rem; /* Adjust size for better visibility */
         color: rgba(0, 0, 0, 0.1); /* Light and subtle text color */
         text-transform: uppercase;
         font-weight: bold;
         letter-spacing: 2px;
         pointer-events: none; /* Prevent interaction */
         user-select: none; /* Prevent text selection */
         z-index: 1; /* Ensure it's visible and above other content */
         }
         .invoice-footer {
         z-index: 2; /* Ensure the footer remains above other content */
         }
      </style>
   </head>
   <body>

      @if (!empty($data['arrAllUnitInvoiceDetail']))
         @foreach ($data['arrAllUnitInvoiceDetail'] as $unitId => $unitData)
            @php
               // Check for errors in current unit detail
               $hasError = !empty($unitData['error']);

               // Set current unit data for this iteration
               // For multiple units, invoice_particular should come from the unit's arrIncomeInvoiceDetail
               $unitInvoiceParticulars = [];
               if (!empty($unitData['arrIncomeInvoiceDetail'][0]['invoice_particulars'])) {
                  // Multiple unit scenario - get particulars from unit data
                  $unitInvoiceParticulars = $unitData['arrIncomeInvoiceDetail'][0]['invoice_particulars'];
                  // Add delayed payment charges if interest amount exists
                  if (!empty($unitData['interestAmount']) && $unitData['interestAmount'] > 0) {
                     $unitInvoiceParticulars[] = [
                        'particular_name' => 'Delayed Payment Charges (' . ($data['rate'] ?? '21') . '% per year)<br> (Previous Due Amount: ' . ($unitData['intrestPrinciple'] ?? 0) . ')',
                        'amount' => $unitData['interestAmount'],
                        'hsn_sac_code' => ''
                     ];
                  }
               } else {
                  // Single unit scenario - use global invoice_particular
                  $unitInvoiceParticulars = $data['invoice_particular'] ?? [];
               }

               $currentUnitData = array_merge($data, [
                  'arrMemberDetail' => $unitData['arrMemberDetail'] ?? $data['arrMemberDetail'] ?? [],
                  'arrIncomeInvoiceDetail' => $unitData['arrIncomeInvoiceDetail'][0] ?? $data['arrIncomeInvoiceDetail'] ?? [],
                  'invoice_particular' => $unitInvoiceParticulars,
                  'invoiceTotalAmount' => $unitData['invoiceTotalAmount'] ?? $data['invoiceTotalAmount'] ?? 0,
                  'advanceAmount' => $unitData['advanceAmount'] ?? $data['advanceAmount'] ?? 0,
                  'grandTotalAmount' => $unitData['grandTotalAmount'] ?? $data['grandTotalAmount'] ?? 0,
                  'outstandingPrincipalAmount' => $unitData['outstandingPrincipalAmount'] ?? $data['outstandingPrincipalAmount'] ?? 0,
                  'outstandingInterestAmount' => $unitData['outstandingInterestAmount'] ?? $data['outstandingInterestAmount'] ?? 0,
                  'balanceDue' => $unitData['balanceDue'] ?? $data['balanceDue'] ?? 0,
                  'rupeesInWord' => $unitData['rupeesInWord'] ?? $data['rupeesInWord'] ?? '',
                  'interestAmount' => $unitData['interestAmount'] ?? $data['interestAmount'] ?? 0,
               ]);
            @endphp

            <div style="width:100%;margin-top:0px;margin-bottom:0px;margin-right:0px;margin-left:0px;padding-top:0px;padding-bottom:0px;padding-right:0px;padding-left:0px;font-size:13px; ">
               <div style="margin-top:0;margin-bottom:0;margin-right:10px;margin-left:10px;padding-left:0px;padding-right:0px;">
                  <div class="invoice-main" style="padding-bottom: 50px;">
                     <div style="width:100%;text-align:center;margin-top:2px;">
                        @if(!empty($data['arrInvoiceSetting']['imageLogoUrl']))
                           <div style="width:100%;text-align:center;margin-bottom:10px;">
                              <img src="{{ $data['arrInvoiceSetting']['imageLogoUrl'] }}"
                                   alt="Society Logo"
                                   style="max-height:80px;max-width:200px;height:auto;width:auto;" />
                           </div>
                        @endif
                        <h2 style="margin-bottom: 0;margin-top: 0px;">
                           {{ $data['arrSocietyDetail']['soc_name'] ?? '' }}
                        </h2>
                        <div style="width:100%;text-align:center;line-height:1.5;color:#000000;margin-top:5px;margin-bottom:15px;font-size:13px;">
                           <strong>
                           {{ $data['arrSocietyDetail']['soc_address_1'] ?? '' }},
                           {{ $data['arrSocietyDetail']['soc_address_2'] ?? '' }},
                           {{ $data['arrSocietyDetail']['soc_landmark'] ?? '' }},
                           {{ $data['arrSocietyDetail']['soc_city_or_town'] ?? '' }},
                           {{ $data['arrSocietyDetail']['soc_state'] ?? '' }}
                           </strong>
                        </div>
                        <div style="width:100%;text-align:center;line-height:1.5;color:#000000;margin-top:2px;margin-bottom:15px;font-size:11px;">
                           <strong>
                               @if(!empty($data['arrSocietyDetail']['soc_office_email']))
                                   Email: {{ $data['arrSocietyDetail']['soc_office_email'] }}
                               @endif

                               @if(!empty($data['arrSocietyDetail']['soc_office_email']) && !empty($data['arrSocietyDetail']['soc_office_mobile']))
                                   |
                               @endif

                               @if(!empty($data['arrSocietyDetail']['soc_office_mobile']))
                                   Phone: {{ $data['arrSocietyDetail']['soc_office_mobile'] }}
                               @endif
                           </strong>
                       </div>
                     </div>
                     <div style="clear:both;"></div>

                     @if($hasError)
                        <div class="alert alert-danger">
                           {!! $unitData['message'] ?? 'Error processing unit data' !!}
                        </div>
                     @else

                        {{-- NO ERROR → continue rendering the full invoice --}}
                        <div style="width:50%;float:left; margin-top:0px;">
                           <table style="border-collapse:collapse;border-spacing:0;margin-bottom:10px;float:left;border-color:#646464;">
                              <tbody>
                                 <tr>
                                    <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;"><strong>To,</strong></td>
                                 </tr>
                                 <tr>
                                    <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;"><strong>Name:</strong></td>
                                    <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;"><strong>{{ $currentUnitData['arrMemberDetail']['member_first_name'] ?? '' }} {{ $currentUnitData['arrMemberDetail']['member_last_name'] ?? '' }}</strong></td>
                                 </tr>
                                 <tr>
                                    <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;"><strong>Parking Unit:</strong></td>
                                    <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;">{{ $currentUnitData['arrMemberDetail']['parking_number'] ?? '' }}</td>
                                 </tr>
                                 <tr>
                                    <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;"><strong>Unit Number:</strong></td>
                                    <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;">{{ $currentUnitData['arrMemberDetail']['soc_building_name'] ?? '' }} / {{ $currentUnitData['arrMemberDetail']['unit_flat_number'] ?? '' }}</td>
                                 </tr>
                                 <tr>
                                    <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;"><strong>Builtup Area:</strong></td>
                                    <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;">{{ $currentUnitData['arrMemberDetail']['unit_area'] ?? '' }} Sqft</td>
                                 </tr>
                                 <tr>
                                    <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;"><strong>GSTIN/UIN:</strong></td>
                                    <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;">{{ $currentUnitData['arrMemberDetail']['gstin'] ?? '' }}</td>
                                 </tr>
                              </tbody>
                           </table>
                        </div>
                        <div style="width:50%;float:right;">
                           <table style="border-collapse:collapse;border-spacing:0;margin-bottom:10px;float:right;border-color:#646464;">
                              <tbody>
                                 <tr>
                                    <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;">&nbsp;</td>
                                    <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;">&nbsp;</td>
                                 </tr>
                                 <tr>
                                    <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;"><strong>Invoice No:</strong></td>
                                    <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;"># {{ $currentUnitData['arrIncomeInvoiceDetail']['unit_invoice_number'] ?? '' }} (Preview)</td>
                                 </tr>
                                 <tr>
                                    <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;"><strong>Invoice Date:</strong></td>
                                    <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;">{{ isset($currentUnitData['arrIncomeInvoiceDetail']['invoice_date']) ? \Carbon\Carbon::parse($currentUnitData['arrIncomeInvoiceDetail']['invoice_date'])->format('M d, Y') : '' }}</td>
                                 </tr>
                                 <tr>
                                    <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;"><strong>Due Date:</strong></td>
                                    <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;">{{ isset($currentUnitData['arrIncomeInvoiceDetail']['due_date']) ? \Carbon\Carbon::parse($currentUnitData['arrIncomeInvoiceDetail']['due_date'])->format('M d, Y') : '' }}</td>
                                 </tr>
                                 <tr>
                                    <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;"><strong>Invoicing Period:</strong></td>
                                    <td style="line-height:1;padding-right:5px;padding-left:5px;vertical-align:top;">{{ isset($currentUnitData['arrIncomeInvoiceDetail']['start_date']) ? \Carbon\Carbon::parse($currentUnitData['arrIncomeInvoiceDetail']['start_date'])->format('M d, Y') : '' }}
to {{ isset($currentUnitData['arrIncomeInvoiceDetail']['end_date']) ? \Carbon\Carbon::parse($currentUnitData['arrIncomeInvoiceDetail']['end_date'])->format('M d, Y') : '' }}</td>
                                 </tr>
                              </tbody>
                           </table>
                        </div>
                        <div style="clear:both;"></div>
                        <div style="width:100%;">
                           <table style="border-collapse:collapse;border-spacing:0;width:100%;">
                              <thead>
                                 <tr>
                                    <th style="border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;background:#f4f4f4;">Particulars</th>
                                    <th style="border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;background:#f4f4f4;">HSN/SAC Code</th>
                                    <th style="text-align:right;border-width:1px;border-style:solid;border-color:#646464;line-height:1;padding:4px;background:#f4f4f4;">Amount <span>(RS.)</span></th>
                                 </tr>
                              </thead>
                              <tbody>
                                 @foreach ($currentUnitData['invoice_particular'] ?? [] as $particular)
                                 <tr>
                                    <td style="text-align:left;border-width:1px;border-style:solid;border-color:#646464;line-height:1.42857;padding:3px 8px;vertical-align:top;">{!! $particular['particular_name'] ?? '' !!}</td>
                                    <td style="text-align:left;border-width:1px;border-style:solid;border-color:#646464;line-height:1.42857;padding:3px 8px;vertical-align:top;">{!! $particular['hsn_sac_code'] ?? '' !!}</td>
                                    <td style="text-align:right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.42857;padding:3px 8px;vertical-align:top;">
                                       {{ isset($particular['amount']) ? number_format((float) $particular['amount'], 2, '.', '') : 'zero value' }}
                                    </td>
                                 </tr>
                                 @endforeach
                                 <tr>
                                    <td colspan="2" style="text-align:right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.42857;padding:3px 8px;vertical-align:top;font-weight:bold;">Invoice Total:</td>
                                    <td style="text-align:right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.42857;padding:3px 8px;vertical-align:top;font-weight:bold;">{{ $currentUnitData['invoiceTotalAmount'] ?? '' }}</td>
                                 </tr>
                                 <tr>
                                    <td rowspan="4" colspan="" style="text-align:left;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:3px 8px;vertical-align:top;">
                                       <strong>
                                       CHEQUE / NEFT / IMPS Payment Instruction:<br><br>
                                       Account Name : {{ $data['arrBankDetail']['account_name'] ?? '' }}<br>
                                       Account Number : {{ $data['arrBankDetail']['account_number'] ?? '' }}<br>
                                       Bank Name : {{ $data['arrBankDetail']['bank_name'] ?? '' }}<br>
                                       Branch Name : {{ $data['arrBankDetail']['branch'] ?? '' }}<br>
                                       IFSC : {{ $data['arrBankDetail']['bank_ifsc'] ?? '' }}
                                       </strong>
                                    </td>
                                    <td colspan="1" style="text-align:right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:3px 8px;vertical-align:top;">Credit/Adjustment:</td>
                                    <td style="text-align:right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:3px 8px;vertical-align:top;">(-) {{ $currentUnitData['advanceAmount'] ?? '' }}</td>
                                 </tr>
                                 <tr>
                                    <td colspan="1" style="text-align:right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:3px 8px;vertical-align:top;"><strong>Grand Total</strong></td>
                                    <td style="text-align:right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:3px 8px;vertical-align:top;"><strong>{{ $currentUnitData['grandTotalAmount'] ?? '' }}</strong></td>
                                 </tr>
                                 <tr>
                                    <td colspan="1" style="text-align:right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:3px 8px;vertical-align:top;">Principal Arrears:</td>
                                    <td style="text-align:right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:3px 8px;vertical-align:top;">(+) {{ $currentUnitData['outstandingPrincipalAmount'] ?? '' }}</td>
                                 </tr>
                                 <tr>
                                    <td colspan="1" style="text-align:right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:3px 8px;vertical-align:top;">Interest Arrears:</td>
                                    <td style="text-align:right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:3px 8px;vertical-align:top;">(+) {{ $currentUnitData['outstandingInterestAmount'] ?? '' }}</td>
                                 </tr>
                                 <tr style="font-size:13px;">
                                    <td colspan="2" style="text-align:right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:3px 8px;vertical-align:top;"><strong>Balance Due:</strong></td>
                                    <td style="text-align:right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:3px 8px;vertical-align:top;font-size:13px;"><strong>{{ $currentUnitData['balanceDue'] ?? '' }}</strong></td>
                                 </tr>
                                 <tr style="font-size:13px;">
                                    <td colspan="3" style="text-align:right;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:3px 8px;vertical-align:top;"><strong>Amount In Words:</strong> <span>{{ $currentUnitData['rupeesInWord'] ?? '' }}</span></td>
                                 </tr>
                              </tbody>
                           </table>
                           <table style="border-collapse:collapse;border-spacing:0;width:100%;">
                           </table>
                           <div>
                           </div>
                        </div>
                        <div style="width: 100%;float: left;padding-top:5px; font-size:13px;">
                           <p style="line-height:12px; margin-top:0px; margin-bottom: 0px;">
                              <strong>
                                 Note:
                                 <br>
                                 @foreach ($data['arrInvoiceGeneralNote'] ?? [] as $item)
                                 @if (!empty($item)) <!-- Skip empty lines -->
                                 {!! nl2br(e($item)) !!}<br>
                                 @endif
                                 @endforeach
                              </strong>
                           </p>
                        </div>
                        <div style="clear:both;"></div>
                     @endif
                  </div>
                  <div class="preview-watermark">Preview</div>
                  <div class="invoice-footer" style="position: fixed; bottom: 50px; width: 100%; text-align: center;">
                     <div style="border-bottom-width: 1px; height: 1px; width: 100%; background: #000; margin: 0 auto;"></div>
                     <div style="width: 100%; font-size: 13px; text-align: center;">
                        <div style="line-height:21px; margin-top:0px; margin-bottom: 0px; text-align: center;">Your society is powered by OneSociety - "Digital Community"</div>
                        <div style="line-height:21px; margin-top:0px; margin-bottom: 0px; text-align: center;">Website: www.cubeoneapp.com | Email: <EMAIL> | Phone: 022-20870052</div>
                     </div>
                     <div style="width: 100%; text-align: center;">
                        <p style="line-height:21px; margin-top:0px; margin-bottom: 0px;">E. &amp; O.E</p>
                     </div>
                  </div>
               </div>
            </div>
         @endforeach
      @endif

      <div style="clear:both;"></div>
      @if (!empty($data['arrLastPeriodPaymentTransaction']) && 2==1)
         <div style="width:100%;">
            <p style="line-height:21px; margin-top: 0px; margin-bottom: 0px;">
               <strong>Payment Receipt:</strong>
               <br>
               <span>
                  Details of payment received from
                  {{ \Carbon\Carbon::createFromFormat('Y-m-d', str_replace('/', '-', $data['arrLastPeriodPaymentTransaction']['invoice_period_detail']['from_date']))->format('M d, Y') }}
                  to
                  {{ \Carbon\Carbon::createFromFormat('Y-m-d', str_replace('/', '-', $data['arrLastPeriodPaymentTransaction']['invoice_period_detail']['to_date']))->format('M d, Y') }}.
               </span>
            </p>
            <table style="border-collapse:collapse;border-spacing:0;width:100%;">
               <thead>
                  <tr>
                     <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;background: #f4f4f4;">Sr No</th>
                     <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;background: #f4f4f4;">Payment Date</th>
                     <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;background: #f4f4f4;">Receipt No#</th>
                     <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;background: #f4f4f4;">Received From</th>
                     <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;background: #f4f4f4;">Mode</th>
                     <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;background: #f4f4f4;">Drawee Bank</th>
                     <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;background: #f4f4f4;">Receipt Note</th>
                     <th style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;background: #f4f4f4;">Amount <span>(RS.)</span></th>
                  </tr>
               </thead>
               <tbody>
                  @php $i = 1; @endphp
                  @foreach ($data['arrLastPeriodPaymentTransaction']['payment_transaction_detail'] as $eachPaymentTransaction)
                     <tr>
                        <td style="text-align:center !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;">{{ $i }}</td>
                        <td style="border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;">
                           {{ \Carbon\Carbon::createFromFormat('Y-m-d', str_replace('/', '-', $eachPaymentTransaction['payment_date']))->format('M d, Y') }}
                        </td>
                        <td style="border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;">{{ $eachPaymentTransaction['receipt_number'] }}</td>
                        <td style="border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;">{{ $eachPaymentTransaction['received_from'] }}</td>
                        <td style="border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;">{{ $eachPaymentTransaction['payment_mode'] }}</td>
                        <td style="border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;">{{ $eachPaymentTransaction['payment_reference'] }}</td>
                        <td style="border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;">{{ $eachPaymentTransaction['payment_note'] }}</td>
                        <td style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;">{{ $eachPaymentTransaction['total_amount'] }}</td>
                     </tr>
                     @php $i++; @endphp
                  @endforeach

                  <tr style="font-size: 13px;">
                     <td colspan="7" style="text-align:left !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;">
                        <strong>Amount In Words:</strong> <span>{{ $data['arrLastPeriodPaymentTransaction']['rupeesInWords'] ?? '' }}</span>
                     </td>
                     <td style="text-align:right !important;border-width:1px;border-style:solid;border-color:#646464;line-height:1.2;padding:4px;">
                        <strong>{{ $data['arrLastPeriodPaymentTransaction']['total'] ?? '' }}</strong>
                     </td>
                  </tr>
               </tbody>
            </table>
         </div>
      @endif

      <div style="width:100%;text-align:right">
         <span>For {{ $data['arrSocietyDetail']['soc_name'] ?? '' }}</span>
      </div>

      <script type="text/javascript">
         function PrintDiv(id) {
            var data = document.getElementById(id).innerHTML;
            var myWindow = window.open('', '', 'height=400,width=600');
            myWindow.document.write('<html><head><title></title>');
            /*optional stylesheet*/ //myWindow.document.write('<link rel="stylesheet" href="main.css" type="text/css" />');
            myWindow.document.write('</head><body >');
            myWindow.document.write(data);
            myWindow.document.write('</body></html>');
            myWindow.document.close(); // necessary for IE >= 10
            //return false;
            myWindow.onload = function() { // necessary if the div contain images
               myWindow.focus(); // necessary for IE >= 10
               myWindow.print();
               myWindow.close();
            };
         }
      </script>
      <script src="chrome-extension://hhojmcideegachlhfgfdhailpfhgknjm/web_accessible_resources/index.js"></script>
   </body>
</html>