<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $society_name ?? 'Member Advances Report' }}</title>
    <link rel="shortcut icon" href="https://stgsociety.chsone.in/images/favicon.png" />
    <style>
        html { font-size: 62.5%; margin: 0; }
        body { font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; font-size: 12px; margin: 0; color: #333; background: #fff; }
        table { background: white; border: solid 1px #ddd; width: 100%; margin: 20px 0; border-collapse: collapse; }
        table th, table td { padding: 6px; border: 1px solid #ddd; text-align: left; font-size: 11px; }
        table thead { background-color: #f0f0f0; }
        .printOption { background: #001941; height: 40px; padding: 10px; color: #fff; }
        .footer { background: #001941; padding: 10px; color: #fff; text-align: right; }
        .main-title { text-align: center; font-size: 1.6em; font-weight: bold; margin-top: 20px; }
        .sub-title { text-align: center; font-size: 1.2em; margin-bottom: 20px; color: #666; }
        .report-date { text-align: left; margin-top: 20px; font-weight: bold; }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        .small-font { font-size: 10px; }
        .total-row { background-color: #e9e9e9; font-weight: bold; }
        .debug { background: #f0f0f0; padding: 10px; margin: 10px 0; font-size: 10px; }
    </style>
</head>
<body>
    <div class="printOption">
        <p>{{ 'Member Advances Report' ?? 'Society Report' }}</p>
    </div>

    <div style="width:100%;text-align:center;padding:10px;">
        <h2 style="margin-bottom:0;margin-top:5px">{{$company_details['society_name'] ?? 'River Walk CHS'}}</h2>
        <h3 style="margin-bottom:0;margin-top:5px">Reg No: {{$company_details['society_reg_num'] ?? 'AKKL123456789'}} | GSTIN/UIN: {{$company_details['gstin'] ?? '27TGDCBA1234G'}}</h3>
        <p style="margin-bottom:0;margin-top:5px">{{$company_details['society_address'] ?? 'Society Address'}}</p>
    </div>

    <div class="sub-title">Member Advance</div>

    {{-- Debug Information --}}
    <div class="debug">
        <strong>Debug Data Structure:</strong><br>
        Data Count: {{ isset($data) ? count($data) : 'No data' }}<br>
        @if(isset($data) && is_array($data))
            @foreach($data as $index => $section)
                Section {{ $index }}: {{ is_array($section) ? count($section) . ' items' : 'Not array' }}<br>
                @if(is_array($section) && !empty($section))
                    First item keys: {{ implode(', ', array_keys($section[0] ?? [])) }}<br>
                @endif
            @endforeach
        @endif
    </div>

    <div class="tableDiv" style="width: 98%; margin: 0 auto;">
        @if(isset($data) && is_array($data) && count($data) > 0)
            @php
                // Handle different data structures
                $reportData = [];
                $summaryData = [];

                // Check if data is directly an array of records
                if (isset($data[0]) && is_array($data[0])) {
                    // If first element is an array, assume it's the report data
                    $reportData = $data[0];
                    // Check for summary data in second element
                    if (isset($data[1]) && is_array($data[1]) && !empty($data[1])) {
                        $summaryData = is_array($data[1][0]) ? $data[1][0] : $data[1];
                    }
                } else {
                    // If data is directly an array of records
                    $reportData = $data;
                }
            @endphp
            
            <table>
                <thead>
                    <tr>
                        <th class="small-font">Building/Unit</th>
                        <th class="small-font text-right">Refundable Balance</th>
                        <th class="small-font text-right">Adjustable Balance</th>
                    </tr>
                </thead>
                <tbody>
                    @if(!empty($reportData))
                        @foreach($reportData as $row)
                            <tr>
                                <td class="small-font">{{ $row['building_unit_name'] ?? ($row['account_name'] ?? 'N/A') }}</td>
                                <td class="small-font text-right">
                                    @if(isset($row['total_refundable']))
                                        {{ $row['total_refundable'] }}
                                    @elseif(isset($row['total_refundable_cr']))
                                        {{ number_format($row['total_refundable_cr'], 2) }}
                                    @else
                                        0.00
                                    @endif
                                </td>
                                <td class="small-font text-right">
                                    @if(isset($row['total_adjustable']))
                                        {{ $row['total_adjustable'] }}
                                    @elseif(isset($row['total_adjustable_cr']) && isset($row['total_adjustable_dr']))
                                        {{ number_format($row['total_adjustable_cr'] - $row['total_adjustable_dr'], 2) }}
                                    @else
                                        0.00
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                    @endif
                    
                    {{-- Summary Row --}}
                    @if(!empty($summaryData))
                        <tr class="total-row">
                            <td class="small-font"><strong>SUM</strong></td>
                            <td class="small-font text-right"><strong>{{ number_format($summaryData['total_summary_refundable'] ?? 0, 2) }}</strong></td>
                            <td class="small-font text-right"><strong>{{ number_format($summaryData['total_summary_adjustable'] ?? 0, 2) }}</strong></td>
                        </tr>
                    @endif
                </tbody>
            </table>

            {{-- Additional Debug for Summary --}}
            <div class="debug">
                <strong>Summary Data:</strong><br>
                @if(!empty($summaryData))
                    @foreach($summaryData as $key => $value)
                        {{ $key }}: {{ $value }}<br>
                    @endforeach
                @else
                    No summary data found
                @endif
            </div>

        @else
            <div class="text-center" style="padding: 20px;">
                <p>No data available for the selected criteria.</p>
                {{-- Debug information when no data --}}
                <div class="debug">
                    <strong>Debug - No Data:</strong><br>
                    Data variable exists: {{ isset($data) ? 'Yes' : 'No' }}<br>
                    Data is array: {{ isset($data) && is_array($data) ? 'Yes' : 'No' }}<br>
                    Data count: {{ isset($data) && is_array($data) ? count($data) : 'N/A' }}<br>
                    @if(isset($data))
                        Data type: {{ gettype($data) }}<br>
                        @if(is_array($data) || is_object($data))
                            Data content: {{ json_encode($data) }}<br>
                        @else
                            Data value: {{ $data }}<br>
                        @endif
                    @endif
                </div>
            </div>
        @endif
    </div>

    <div class="report-date">
        Report date: {{ $report_date ?? date('d/m/Y') }}
    </div>

    <div class="footer">
        <span>Powered By:</span> <img src="https://stgsociety.chsone.in/images/logo.png" alt="CHSONE" style="vertical-align: middle;">
    </div>
</body>
</html>
